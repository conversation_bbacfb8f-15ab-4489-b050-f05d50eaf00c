package cn.hznanf.system.api.service.impl.purchase;

import cn.hznanf.common.enums.AllocateOrderStatusEnum;
import cn.hznanf.common.enums.InOutBoundOrderStatusEnum;
import cn.hznanf.common.enums.MiniProgramPageDict;
import cn.hznanf.common.enums.PurchaseOrderStatusEnum;
import cn.hznanf.common.enums.SourceOrderTypeEnum;
import cn.hznanf.common.exception.ServiceException;
import cn.hznanf.common.pojo.PagePurchaseInboundResult;
import cn.hznanf.common.pojo.PageResult;
import cn.hznanf.common.util.object.AssertUtil;
import cn.hznanf.common.util.string.CodeGeneratorUtil;
import cn.hznanf.system.api.ErrorCodeConstants;
import cn.hznanf.system.api.convert.InOutBoundOrderConvert;
import cn.hznanf.system.api.dataobject.Employee;
import cn.hznanf.system.api.dataobject.contract.ContractManager;
import cn.hznanf.system.api.dataobject.material.MaterialDO;
import cn.hznanf.system.api.dataobject.project.ProjectDO;
import cn.hznanf.system.api.dataobject.purchase.AllocateOrder;
import cn.hznanf.system.api.dataobject.purchase.AllocateOrderLine;
import cn.hznanf.system.api.dataobject.purchase.InBoundOrder;
import cn.hznanf.system.api.dataobject.purchase.InBoundOrderLine;
import cn.hznanf.system.api.dataobject.purchase.OutBoundOrder;
import cn.hznanf.system.api.dataobject.purchase.OutBoundOrderLine;
import cn.hznanf.system.api.dataobject.purchase.ProjectStock;
import cn.hznanf.system.api.dataobject.purchase.ProjectStockRecord;
import cn.hznanf.system.api.dataobject.purchase.PurchaseOrder;
import cn.hznanf.system.api.dataobject.purchase.PurchaseOrderLine;
import cn.hznanf.system.api.dataobject.purchase.PurchasePayLogDO;
import cn.hznanf.system.api.dataobject.purchase.SupplierManagerDO;
import cn.hznanf.system.api.service.dict.DictDataService;
import cn.hznanf.system.api.service.mapper.contract.ContractManagerMapper;
import cn.hznanf.system.api.service.mapper.material.MaterialMapper;
import cn.hznanf.system.api.service.mapper.project.ProjectMapper;
import cn.hznanf.system.api.service.mapper.project.ProjectStockMapper;
import cn.hznanf.system.api.service.mapper.project.ProjectStockRecordMapper;
import cn.hznanf.system.api.service.mapper.purchase.AllocateOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.AllocateOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.InBoundOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.InBoundOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.OutBoundOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.OutBoundOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseBillLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseBillMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchasePayLogMapper;
import cn.hznanf.system.api.service.mapper.purchase.SupplierManagerMapper;
import cn.hznanf.system.api.service.mapper.user.EmployeeMapper;
import cn.hznanf.system.api.service.purchase.InOutBoundOrderService;
import cn.hznanf.system.api.service.transaction.TransactionService;
import cn.hznanf.system.api.service.user.EmployeeService;
import cn.hznanf.system.api.vo.purchase.InBoundOrderVO;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderCreateVO;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderCreateVO.SourceOrderLine;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderLineVO;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderPageReqVO;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderUpdateVO;
import cn.hznanf.system.api.vo.purchase.InOutBoundOrderUpdateVO.InOutBoundOrderLine;
import cn.hznanf.system.api.vo.purchase.OutBoundOrderVO;
import cn.hznanf.system.api.vo.purchase.PurchaseOrderShippedVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


/**
 * description 出入库单
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@Service
@Slf4j
public class InOutBoundOrderServiceImpl implements InOutBoundOrderService {

  @Resource
  private PurchaseOrderMapper purchaseOrderMapper;
  @Resource
  private PurchaseOrderLineMapper purchaseOrderLineMapper;
  @Resource
  private ProjectStockMapper projectStockMapper;
  @Resource
  private ProjectStockRecordMapper projectStockRecordMapper;
  @Resource
  private AllocateOrderMapper allocateOrderMapper;
  @Resource
  private AllocateOrderLineMapper allocateOrderLineMapper;
  @Resource
  private InBoundOrderMapper inBoundOrderMapper;
  @Resource
  private InBoundOrderLineMapper inBoundOrderLineMapper;
  @Resource
  private OutBoundOrderMapper outBoundOrderMapper;
  @Resource
  private OutBoundOrderLineMapper outBoundOrderLineMapper;
  @Resource
  private TransactionService transactionService;
  @Resource
  private ProjectMapper projectMapper;
  @Resource
  private EmployeeMapper employeeMapper;
  @Resource
  private EmployeeService employeeService;
  @Resource
  private MaterialMapper materialMapper;
  @Resource
  private SupplierManagerMapper supplierManagerMapper;
  @Resource
  private DictDataService dictDataService;
  @Resource
  private PurchaseBillLineMapper purchaseBillLineMapper;
  @Resource
  private PurchaseBillMapper purchaseBillMapper;
  @Resource
  private ContractManagerMapper contractManagerMapper;

  @Resource
  private PurchasePayLogMapper purchasePayLogMapper;


  @Override
  public PageResult<OutBoundOrderVO> getPage(InOutBoundOrderPageReqVO reqVO) {
    PageResult<OutBoundOrder> pageResult = outBoundOrderMapper.selectPage(reqVO);
    PageResult<OutBoundOrderVO> inOutBoundOrderVOPageResult = InOutBoundOrderConvert.INSTANCE.outBoundDo2vo(
        pageResult);
    final List<OutBoundOrderVO> list = inOutBoundOrderVOPageResult.getList();
    if (CollectionUtils.isEmpty(list)) {
      return inOutBoundOrderVOPageResult;
    }
    Set<Long> collect = list.stream().map(OutBoundOrderVO::getProjectId)
        .collect(Collectors.toSet());

    List<String> allotOrderCodeList = list.stream().map(OutBoundOrderVO::getSourceOrderCode)
        .collect(Collectors.toList());
    List<AllocateOrder> allocateOrders = allocateOrderMapper.selectList(AllocateOrder::getCode,
        allotOrderCodeList);
    Map<String, Long> allotMap = allocateOrders.stream()
        .collect(Collectors.toMap(AllocateOrder::getCode, AllocateOrder::getTargetProjectId));
    List<Long> targetProjectIdList = allocateOrders.stream().map(AllocateOrder::getTargetProjectId)
        .collect(Collectors.toList());
    collect.addAll(targetProjectIdList);
    Set<Long> userIds = list.stream().map(OutBoundOrderVO::getCreator).collect(Collectors.toSet());
    Map<Long, String> map = projectMapper.selectBatchIds(collect).stream()
        .collect(Collectors.toMap(ProjectDO::getId, ProjectDO::getName));

    Map<Long, String> userMap = employeeMapper.selectBatchIds(userIds).stream()
        .collect(Collectors.toMap(Employee::getId, Employee::getName));
    for (OutBoundOrderVO boundOrderVO : list) {
      Long projectId = boundOrderVO.getProjectId();
      Long targetProjectId = allotMap.get(boundOrderVO.getSourceOrderCode());

      boundOrderVO.setTargetProjectName(map.get(targetProjectId));
      boundOrderVO.setProjectName(map.get(projectId));
      boundOrderVO.setCreatorName(userMap.get(boundOrderVO.getCreator()));
    }
    return inOutBoundOrderVOPageResult;
  }

  @Override
  public void in(InOutBoundOrderUpdateVO req) {
    log.info("出入库单操作入参：{}", JSON.toJSONString(req));
    Long id = req.getId();
    InBoundOrder inOutBoundOrder = inBoundOrderMapper.selectById(id);
    AssertUtil.isTrue(
        InOutBoundOrderStatusEnum.WAIT_IN.getStatus().equals(inOutBoundOrder.getStatus()),
        ErrorCodeConstants.ONLY_WAIT_IN_CAN_BE_IN);
    List<InOutBoundOrderLine> inOutBoundOrderLines1 = req.getInOutBoundOrderLines();
    if (CollectionUtils.isEmpty(inOutBoundOrderLines1)) {
      throw new ServiceException(ErrorCodeConstants.IN_OUT_BOUND_ORDER_LINE_EMPTY);
    }
    boolean isPurchase = inOutBoundOrder.getSourceOrderCode().startsWith("PO");
    for (InOutBoundOrderLine inOutBoundOrderLine : inOutBoundOrderLines1) {
      BigDecimal inQty = inOutBoundOrderLine.getInQty();
      AssertUtil.isTrue(Objects.nonNull(inQty) && (inQty.compareTo(BigDecimal.ZERO) > 0),
          ErrorCodeConstants.IN_QTY_MUST_GREATER_THAN_ZERO);
      if (isPurchase) {
        BigDecimal inSettleQty = inOutBoundOrderLine.getInSettleQty();
        AssertUtil.isTrue(
            Objects.nonNull(inSettleQty) && (inSettleQty.compareTo(BigDecimal.ZERO) > 0),
            ErrorCodeConstants.IN_SETTLE_QTY_MUST_GREATER_THAN_ZERO);
      }
    }

    inOutBoundOrder.setAttachments(req.getAttachments());
    inOutBoundOrder.setRemark(req.getRemark());
    inOutBoundOrder.setConfirmTime(LocalDateTime.now());
    inOutBoundOrder.setConfirmUserId(req.getUserId());
    inOutBoundOrder.setStatus(InOutBoundOrderStatusEnum.DONE.getStatus());
    List<InBoundOrderLine> inOutBoundOrderLines = inBoundOrderLineMapper.selectByForeignKey(id);
    Map<Long, InOutBoundOrderUpdateVO.InOutBoundOrderLine> inOutBoundOrderLineMap = inOutBoundOrderLines1.stream()
        .collect(Collectors.toMap(InOutBoundOrderUpdateVO.InOutBoundOrderLine::getId,
            Function.identity()));

    Long projectId = inOutBoundOrder.getProjectId();
    Set<String> materialCodes = inOutBoundOrderLines.stream().map(InBoundOrderLine::getMaterialCode)
        .collect(Collectors.toSet());
    List<ProjectStock> projectStocks = projectStockMapper.selectByProjectIdAndMaterialCodes(
        projectId, materialCodes);
    Map<String, ProjectStock> stockMap = projectStocks.stream()
        .collect(Collectors.toMap(ProjectStock::getMaterialCode, Function.identity()));
    List<ProjectStock> stockInserts = Lists.newArrayList();
    List<ProjectStock> stockUpdates = Lists.newArrayList();
    List<ProjectStockRecord> stockRecordInserts = Lists.newArrayList();

    Map<String, BigDecimal> inQtyMap = new HashMap<>();
    Map<String, BigDecimal> settleQtyMap = new HashMap<>();
    for (InBoundOrderLine inBoundOrderLine : inOutBoundOrderLines) {
      InOutBoundOrderUpdateVO.InOutBoundOrderLine orderLine = inOutBoundOrderLineMap.get(
          inBoundOrderLine.getId());
      if (Objects.isNull(orderLine)) {
        continue;
      }
      BigDecimal inQty = orderLine.getInQty();

      BigDecimal inSettleQty = orderLine.getInSettleQty();

      inQtyMap.put(inBoundOrderLine.getMaterialCode(), inQty);
      settleQtyMap.put(inBoundOrderLine.getMaterialCode(), inSettleQty);

      inBoundOrderLine.setRealQty(inQty);
      inBoundOrderLine.setNewRealSettleQty(inSettleQty);

      ProjectStock projectStock = stockMap.get(inBoundOrderLine.getMaterialCode());
      ProjectStockRecord stockRecord = new ProjectStockRecord();
      stockRecord.setProjectId(projectId);
      stockRecord.setMaterialCode(inBoundOrderLine.getMaterialCode());
      stockRecord.setMaterialName(inBoundOrderLine.getMaterialName());
      stockRecord.setAlterQty(inQty);
      stockRecord.setAfterAvailableQty(inQty);
      stockRecord.setAfterTotalQty(inQty);
      stockRecord.setAlterType(isPurchase ? 0 : 4);
      if (isPurchase) {
        BigDecimal price = inBoundOrderLine.getPrice()
            .multiply(inBoundOrderLine.getDeliverySettleQty())
            .divide(inBoundOrderLine.getQty(), RoundingMode.HALF_DOWN);
        stockRecord.setPrice(price);
      } else {
        stockRecord.setPrice(inBoundOrderLine.getPrice());
      }
      stockRecord.setInOutBoundOrderCode(inOutBoundOrder.getCode());
      stockRecord.setRemark("签收入库");
      stockRecordInserts.add(stockRecord);
      if (Objects.isNull(projectStock)) {
        projectStock = new ProjectStock();
        projectStock.setProjectId(projectId);
        projectStock.setMaterialCode(inBoundOrderLine.getMaterialCode());
        projectStock.setMaterialName(inBoundOrderLine.getMaterialName());
        projectStock.setTotalQty(inQty);
        projectStock.setAvailableQty(inQty);
        projectStock.setUsedQty(BigDecimal.ZERO);
        projectStock.setRemark("入库初始化库存");
        stockInserts.add(projectStock);
        continue;
      }
      projectStock.setAvailableQty(projectStock.getAvailableQty().add(inQty));
      projectStock.setTotalQty(projectStock.getTotalQty().add(inQty));
      stockRecord.setAfterAvailableQty(projectStock.getAvailableQty());
      stockRecord.setAfterTotalQty(projectStock.getTotalQty());
      stockUpdates.add(projectStock);

    }
    String sourceOrderCode = inOutBoundOrder.getSourceOrderCode();
    List<InBoundOrder> inOutBoundOrders = inBoundOrderMapper.selectBySourceOrderCode(
        sourceOrderCode);
    Optional<InBoundOrder> first = inOutBoundOrders.stream().filter(
        s -> !s.getId().equals(id) && InOutBoundOrderStatusEnum.WAIT_IN.getStatus()
            .equals(s.getStatus())).findFirst();
    PurchaseOrder purchaseOrder = null;
    AllocateOrder allocateOrder = null;
    List<PurchasePayLogDO> purchasePayLogDOS = Lists.newArrayList();
    if (inOutBoundOrder.getSourceOrderType().equals(SourceOrderTypeEnum.ALLOCATE_ORDER.getType())) {
      allocateOrder = allocateOrderMapper.selectByCode(sourceOrderCode);
      if (AllocateOrderStatusEnum.WAIT_IN.getStatus().equals(allocateOrder.getStatus())) {
        allocateOrder.setStatus(first.isPresent() ? allocateOrder.getStatus()
            : AllocateOrderStatusEnum.DONE.getStatus());
      }
    } else {
      purchaseOrder = purchaseOrderMapper.selectByCode(sourceOrderCode);
      if (PurchaseOrderStatusEnum.ALL_OUT.getStatus().equals(purchaseOrder.getStatus())) {
        purchaseOrder.setStatus(first.isPresent() ? purchaseOrder.getStatus()
            : PurchaseOrderStatusEnum.DONE.getStatus());
      }
      String contractNo = purchaseOrder.getContractNo();
      if (StringUtils.hasText(contractNo)) {
        ContractManager contractManager = contractManagerMapper.selectByCode(contractNo);
        if (0 == contractManager.getPayRule()) {
          List<PurchaseOrderLine> purchaseOrderLines = purchaseOrderLineMapper.selectListByPurchaseOrderId(
              purchaseOrder.getId());
          SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
              purchaseOrder.getSupplierId());
          for (PurchaseOrderLine purchaseOrderLine : purchaseOrderLines) {
            BigDecimal qty = settleQtyMap.get(purchaseOrderLine.getMaterialCode());
            if (Objects.isNull(qty)) {
              continue;
            }
            // 一口价合同处理
            PurchasePayLogDO purchasePayLogDO = new PurchasePayLogDO();
            purchasePayLogDO.setPaymentCode("");
            purchasePayLogDO.setPayTime(LocalDateTime.now());
            purchasePayLogDO.setPurchaseCode(purchaseOrder.getCode());
            purchasePayLogDO.setContactCode(contractNo);
            purchasePayLogDO.setProjectId(purchaseOrder.getProjectId());
            purchasePayLogDO.setMaterialName(purchaseOrderLine.getMaterialName());
            purchasePayLogDO.setPrice(purchaseOrderLine.getPrice());
            purchasePayLogDO.setTotalAmt(purchaseOrderLine.getPrice().multiply(qty));
            purchasePayLogDO.setReceiptName(supplierManagerDO.getName());
            purchasePayLogDO.setQty(qty);
            purchasePayLogDOS.add(purchasePayLogDO);
          }
        }

      }
    }
    PurchaseOrder finalPurchaseOrder = purchaseOrder;
    AllocateOrder finalAllocateOrder = allocateOrder;

    transactionService.transaction(() -> {
      inBoundOrderMapper.updateById(inOutBoundOrder);
      inBoundOrderLineMapper.updateBatch(inOutBoundOrderLines, 1000);
      projectStockRecordMapper.insertBatch(stockRecordInserts);
      if (CollectionUtils.isNotEmpty(stockInserts)) {
        projectStockMapper.insertBatch(stockInserts);
      }
      if (CollectionUtils.isNotEmpty(stockUpdates)) {
        projectStockMapper.updateBatch(stockUpdates, 1000);
      }
      if (Objects.nonNull(finalPurchaseOrder)) {
        purchaseOrderMapper.updateById(finalPurchaseOrder);
      }
      if (Objects.nonNull(finalAllocateOrder)) {
        allocateOrderMapper.updateById(finalAllocateOrder);
      }
      if (CollectionUtils.isNotEmpty(purchasePayLogDOS)) {
        purchasePayLogMapper.insertBatch(purchasePayLogDOS);
      }
      return null;
    });
  }

  @Override
  public PageResult<InBoundOrderVO> getInPage(InOutBoundOrderPageReqVO reqVO) {

    PageResult<InBoundOrder> pageResult = inBoundOrderMapper.selectPage(reqVO);
    PageResult<InBoundOrderVO> inOutBoundOrderVOPageResult = InOutBoundOrderConvert.INSTANCE.inBoundDo2vo(
        pageResult);
    final List<InBoundOrderVO> list = inOutBoundOrderVOPageResult.getList();
    if (CollectionUtils.isEmpty(list)) {
      return inOutBoundOrderVOPageResult;
    }
    Set<Long> projectIdSet = list.stream().map(InBoundOrderVO::getProjectId)
        .collect(Collectors.toSet());

    Map<String, Long> codeMap = Maps.newHashMap();
    Map<Long, List<InBoundOrderLine>> inBoundOrderLineMap = new HashMap<>();
    if (!Objects.isNull(reqVO.getSourceOrderType())) {
      if (0 == reqVO.getSourceOrderType()) {
        List<String> sourceOrderCodeList = list.stream().map(InBoundOrderVO::getSourceOrderCode)
            .collect(Collectors.toList());
        List<AllocateOrder> allocateOrders = allocateOrderMapper.selectList(AllocateOrder::getCode,
            sourceOrderCodeList);
        allocateOrders.stream().filter(Objects::nonNull).forEach(s -> {
          projectIdSet.add(s.getSourceProjectId());
          codeMap.put(s.getCode(), s.getSourceProjectId());
        });
      } else {
        List<Long> inBoundOrderIdList = list.stream().map(InBoundOrderVO::getId)
            .collect(Collectors.toList());
        List<InBoundOrderLine> inBoundOrderLines = inBoundOrderLineMapper.selectList(
            InBoundOrderLine::getInOutBoundOrderId, inBoundOrderIdList);
        inBoundOrderLineMap = inBoundOrderLines.stream()
            .collect(Collectors.groupingBy(InBoundOrderLine::getInOutBoundOrderId));
      }
    }

    Map<Long, String> map = projectMapper.selectBatchIds(projectIdSet).stream()
        .collect(Collectors.toMap(ProjectDO::getId, ProjectDO::getName));
    Set<Long> employeeIdSet = list.stream().map(InBoundOrderVO::getConfirmUserId)
        .collect(Collectors.toSet());
    Set<Long> supplierIdSet = list.stream().map(InBoundOrderVO::getSupplierId)
        .filter(Objects::nonNull).collect(Collectors.toSet());
    Map<Long, String> employeeIdName = employeeMapper.selectBatchIds(employeeIdSet).stream()
        .collect(Collectors.toMap(Employee::getId, Employee::getName));
    Map<Long, String> supplierMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(supplierIdSet)) {
      supplierMap = supplierManagerMapper.selectBatchIds(supplierIdSet).stream()
          .collect(Collectors.toMap(SupplierManagerDO::getId, SupplierManagerDO::getName));
    }
    for (InBoundOrderVO inOutBoundOrderVO : list) {
      inOutBoundOrderVO.setProjectName(map.get(inOutBoundOrderVO.getProjectId()));
      inOutBoundOrderVO.setConfirmUserName(
          employeeIdName.get(inOutBoundOrderVO.getConfirmUserId()));
      inOutBoundOrderVO.setSupplierName(supplierMap.get(inOutBoundOrderVO.getSupplierId()));
      if (Objects.isNull(reqVO.getSourceOrderType())) {
        continue;
      }
      if (0 == reqVO.getSourceOrderType()) {
        inOutBoundOrderVO.setSourceProjectName(
            map.get(codeMap.get(inOutBoundOrderVO.getSourceOrderCode())));
      } else {
        List<InBoundOrderLine> inBoundOrderLines = inBoundOrderLineMap.get(
            inOutBoundOrderVO.getId());
        BigDecimal totalSettledAmt = inBoundOrderLines.stream().map(InBoundOrderLine::getSettledAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPayedAmt = inBoundOrderLines.stream().map(InBoundOrderLine::getPayedAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmt = inBoundOrderLines.stream().map(InBoundOrderLine::getTotalAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        inOutBoundOrderVO.setTotalSettledAmt(totalSettledAmt);
        inOutBoundOrderVO.setTotalPayedAmt(totalPayedAmt);
        inOutBoundOrderVO.setTotalAmt(totalAmt);
      }
    }
    return inOutBoundOrderVOPageResult;
  }


  @Override
  public PagePurchaseInboundResult<InBoundOrderVO> getPurchaseInPage(
      InOutBoundOrderPageReqVO reqVO) {
    PagePurchaseInboundResult<InBoundOrderVO> result = new PagePurchaseInboundResult<>();

    PageResult<InBoundOrder> pageResult = inBoundOrderMapper.selectPage(reqVO);

    PageResult<InBoundOrderVO> inOutBoundOrderVOPageResult = InOutBoundOrderConvert.INSTANCE.inBoundDo2vo(
        pageResult);
    final List<InBoundOrderVO> list = inOutBoundOrderVOPageResult.getList();
    if (CollectionUtils.isEmpty(list)) {
      return result;
    }
    result.setTotal(pageResult.getTotal());
    result.setList(inOutBoundOrderVOPageResult.getList());

    Set<Long> projectIdSet = list.stream().map(InBoundOrderVO::getProjectId)
        .collect(Collectors.toSet());

    Map<Long, List<InBoundOrderLine>> inBoundOrderLineMap;

    List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectAllInbound(reqVO);
    List<Long> inBoundOrderIdList = inBoundOrders.stream().map(InBoundOrder::getId)
        .collect(Collectors.toList());

    List<InBoundOrderLine> inBoundOrderLines = inBoundOrderLineMapper.selectList(
        InBoundOrderLine::getInOutBoundOrderId, inBoundOrderIdList);

    BigDecimal allTotalSettledAmt = inBoundOrderLines.stream()
        .map(InBoundOrderLine::getSettledAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal allTotalPayedAmt = inBoundOrderLines.stream().map(InBoundOrderLine::getPayedAmt)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal allTotalAmt = inBoundOrderLines.stream().map(InBoundOrderLine::getTotalAmt)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    result.setAllTotalAmt(allTotalAmt);
    result.setAllTotalSettledAmt(allTotalSettledAmt);
    result.setAllTotalPayedAmt(allTotalPayedAmt);

    inBoundOrderLineMap = inBoundOrderLines.stream()
        .collect(Collectors.groupingBy(InBoundOrderLine::getInOutBoundOrderId));

    Map<Long, String> map = projectMapper.selectBatchIds(projectIdSet).stream()
        .collect(Collectors.toMap(ProjectDO::getId, ProjectDO::getName));
    Set<Long> employeeIdSet = list.stream().map(InBoundOrderVO::getConfirmUserId)
        .collect(Collectors.toSet());
    Set<Long> supplierIdSet = list.stream().map(InBoundOrderVO::getSupplierId)
        .filter(Objects::nonNull).collect(Collectors.toSet());
    Map<Long, String> employeeIdName = employeeMapper.selectBatchIds(employeeIdSet).stream()
        .collect(Collectors.toMap(Employee::getId, Employee::getName));
    Map<Long, String> supplierMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(supplierIdSet)) {
      supplierMap = supplierManagerMapper.selectBatchIds(supplierIdSet).stream()
          .collect(Collectors.toMap(SupplierManagerDO::getId, SupplierManagerDO::getName));
    }
    for (InBoundOrderVO inOutBoundOrderVO : list) {
      inOutBoundOrderVO.setProjectName(map.get(inOutBoundOrderVO.getProjectId()));
      inOutBoundOrderVO.setConfirmUserName(
          employeeIdName.get(inOutBoundOrderVO.getConfirmUserId()));
      inOutBoundOrderVO.setSupplierName(supplierMap.get(inOutBoundOrderVO.getSupplierId()));
      if (Objects.isNull(reqVO.getSourceOrderType())) {
        continue;
      }
      List<InBoundOrderLine> inBoundOrderLinesById = inBoundOrderLineMap.get(
          inOutBoundOrderVO.getId());
      BigDecimal totalSettledAmt = inBoundOrderLinesById.stream()
          .map(InBoundOrderLine::getSettledAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal totalPayedAmt = inBoundOrderLinesById.stream().map(InBoundOrderLine::getPayedAmt)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal totalAmt = inBoundOrderLinesById.stream().map(InBoundOrderLine::getTotalAmt)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      inOutBoundOrderVO.setTotalSettledAmt(totalSettledAmt);
      inOutBoundOrderVO.setTotalPayedAmt(totalPayedAmt);
      inOutBoundOrderVO.setTotalAmt(totalAmt);

    }
    return result;
  }

  @Override
  public InBoundOrderVO getInDetail(Long id) {
    InBoundOrder inOutBoundOrder = inBoundOrderMapper.selectById(id);
    InBoundOrderVO do2DetailVo = InOutBoundOrderConvert.INSTANCE.inBoundDo2DetailVo(
        inOutBoundOrder);
    List<InBoundOrderLine> orderLineDOS = inBoundOrderLineMapper.selectByForeignKey(id);
    List<InOutBoundOrderLineVO> lineVOList = InOutBoundOrderConvert.INSTANCE.batchLineDo2vo(
        orderLineDOS);
    for (InOutBoundOrderLineVO inOutBoundOrderLineVO : lineVOList) {
      setMaterial(inOutBoundOrderLineVO);
    }

    Integer sourceOrderType = inOutBoundOrder.getSourceOrderType();
    if (SourceOrderTypeEnum.ALLOCATE_ORDER.getType().equals(sourceOrderType)) {
      AllocateOrder allocateOrder = allocateOrderMapper.selectByCode(
          inOutBoundOrder.getSourceOrderCode());
      do2DetailVo.setSourceContactName(allocateOrder.getSourceContactName());
      do2DetailVo.setSourceProjectAddress(allocateOrder.getSourceProjectAddress());
      ProjectDO projectDO = projectMapper.selectById(allocateOrder.getSourceProjectId());
      do2DetailVo.setSourceProjectName(projectDO.getName());
    }
    if (SourceOrderTypeEnum.PURCHASE_ORDER.getType().equals(sourceOrderType)) {
      PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByCode(
          inOutBoundOrder.getSourceOrderCode());
      do2DetailVo.setSourceContactPhone(purchaseOrder.getSendContactPhone());
      do2DetailVo.setSourceContactName(purchaseOrder.getSendContactPerson());
      do2DetailVo.setSourceProjectAddress(purchaseOrder.getSendAddress());
      Long supplierId = purchaseOrder.getSupplierId();
      SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(supplierId);
      String name = supplierManagerDO.getName();
      do2DetailVo.setSupplierName(name);
    }
    if (Objects.nonNull(do2DetailVo.getSupplierId())) {
      final SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
          do2DetailVo.getSupplierId());
      do2DetailVo.setSupplierName(supplierManagerDO.getName());
    }

    if (do2DetailVo.getSettled()) {
      do2DetailVo.setRelatedBillCode(purchaseBillMapper.getCodeByInCode(do2DetailVo.getCode()));
    }
    do2DetailVo.setInOutBoundOrderLineVOList(lineVOList);
    Long projectId = do2DetailVo.getProjectId();
    ProjectDO projectDO = projectMapper.selectById(projectId);
    do2DetailVo.setProjectName(projectDO.getName());
    if (Objects.nonNull(do2DetailVo.getConfirmUserId())) {
      Employee employee = employeeMapper.selectById(do2DetailVo.getConfirmUserId());
      do2DetailVo.setConfirmUserName(employee.getName());
    }
    return do2DetailVo;
  }

  @Override
  public List<PurchaseOrderShippedVO> shippedDetail(String purchaseOrderCode) {
    List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectList(
        InBoundOrder::getSourceOrderCode, purchaseOrderCode);
    if (CollectionUtils.isEmpty(inBoundOrders)) {
      return new ArrayList<>();
    }
    Map<Long, InBoundOrder> boundOrderMap = inBoundOrders.stream()
        .collect(Collectors.toMap(InBoundOrder::getId, Function.identity()));
    Set<Long> inBoundIdSet = inBoundOrders.stream().map(InBoundOrder::getId)
        .collect(Collectors.toSet());
    List<InBoundOrderLine> inBoundOrderLines = inBoundOrderLineMapper.selectList(
        InBoundOrderLine::getInOutBoundOrderId, inBoundIdSet);
    List<PurchaseOrderShippedVO> purchaseOrderShippedVOS = new ArrayList<>();
    Set<String> materialCodesSet = inBoundOrderLines.stream().map(InBoundOrderLine::getMaterialCode)
        .collect(Collectors.toSet());
    List<MaterialDO> materialDOS = materialMapper.selectByCodes(materialCodesSet);
    Map<String, MaterialDO> materialDOMap = materialDOS.stream()
        .collect(Collectors.toMap(MaterialDO::getCode, Function.identity()));
    for (InBoundOrderLine inBoundOrderLine : inBoundOrderLines) {
      MaterialDO materialDO = materialDOMap.get(inBoundOrderLine.getMaterialCode());
      PurchaseOrderShippedVO purchaseOrderShippedVO = new PurchaseOrderShippedVO();
      InBoundOrder inBoundOrder = boundOrderMap.get(inBoundOrderLine.getInOutBoundOrderId());
      purchaseOrderShippedVO.setCode(inBoundOrder.getCode());
      purchaseOrderShippedVO.setCreateTime(inBoundOrderLine.getCreateTime());
      purchaseOrderShippedVO.setMaterialCode(inBoundOrderLine.getMaterialCode());
      purchaseOrderShippedVO.setMaterialName(inBoundOrderLine.getMaterialName());
      purchaseOrderShippedVO.setQty(inBoundOrderLine.getQty());
      purchaseOrderShippedVO.setDeliverySettleQty(inBoundOrderLine.getDeliverySettleQty());
      purchaseOrderShippedVO.setRealQty(inBoundOrderLine.getRealQty());
      purchaseOrderShippedVO.setNewRealSettleQty(inBoundOrderLine.getNewRealSettleQty());
      purchaseOrderShippedVO.setDeliveryRemark(inBoundOrder.getDeliveryRemark());
      purchaseOrderShippedVO.setTruckDriverMobile(inBoundOrder.getTruckDriverMobile());
      purchaseOrderShippedVO.setTruckDriverName(inBoundOrder.getTruckDriverName());

      purchaseOrderShippedVO.setUnit(
          dictDataService.getLabelCache("materiel_unit", "" + materialDO.getMeasurementUnit()));
      purchaseOrderShippedVO.setSettleUnitName(
          dictDataService.getLabelCache("materiel_unit", "" + inBoundOrderLine.getSettleUnit()));
      purchaseOrderShippedVOS.add(purchaseOrderShippedVO);
    }
    return purchaseOrderShippedVOS;
  }

  @Override
  public void dealUnSettled() {
    LambdaQueryWrapper<InBoundOrder> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(InBoundOrder::getSettled, 0);
    queryWrapper.eq(InBoundOrder::getStatus, InOutBoundOrderStatusEnum.DONE.getStatus());
    queryWrapper.eq(InBoundOrder::getSourceOrderType, SourceOrderTypeEnum.PURCHASE_ORDER.getType());
    List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectList(queryWrapper);
    if (CollectionUtils.isEmpty(inBoundOrders)) {
      return;
    }
    List<Long> inBoundOrderIds = inBoundOrders.stream().map(InBoundOrder::getId)
        .collect(Collectors.toList());

    List<InBoundOrderLine> inBoundOrderLines = inBoundOrderLineMapper.selectList(
        InBoundOrderLine::getInOutBoundOrderId, inBoundOrderIds);

    Map<Long, List<InBoundOrderLine>> inBoundOrderLineMap = inBoundOrderLines.stream()
        .collect(Collectors.groupingBy(InBoundOrderLine::getInOutBoundOrderId));

    for (InBoundOrder inBoundOrder : inBoundOrders) {
      List<InBoundOrderLine> inBoundOrderLines1 = inBoundOrderLineMap.get(inBoundOrder.getId());
      boolean anyMatch = inBoundOrderLines1.stream().anyMatch(InBoundOrderLine::isSettled);
      if (anyMatch) {
        continue;
      }
      inBoundOrder.setSettled(true);
      inBoundOrderMapper.updateById(inBoundOrder);
    }


  }

  @Override
  @Transactional
  public void createByOutBound(InOutBoundOrderCreateVO req) {
    //供应商发货，调拨单出库

    //生成供应商，调出方出库单，采购商，调入方入库单
    InBoundOrder inBoundOrder = new InBoundOrder();
    inBoundOrder.setCode(CodeGeneratorUtil.generateCode("IN"));
    inBoundOrder.setSourceOrderType(req.getSourceOrderType());
    inBoundOrder.setStatus(InOutBoundOrderStatusEnum.WAIT_IN.getStatus());
    inBoundOrder.setTruckNo(req.getTruckNo());
    inBoundOrder.setTruckDriverMobile(req.getTruckDriverMobile());
    inBoundOrder.setTruckDriverName(req.getTruckDriverName());

    OutBoundOrder outBoundOrder = outBoundOrderMapper.selectById(req.getSourceOrderId());
    outBoundOrder.setStatus(InOutBoundOrderStatusEnum.DONE.getStatus());
    outBoundOrder.setTruckNo(req.getTruckNo());
    outBoundOrder.setTruckDriverMobile(req.getTruckDriverMobile());
    outBoundOrder.setTruckDriverName(req.getTruckDriverName());
    outBoundOrder.setRemark(req.getRemark());
    outBoundOrder.setAttachments(req.getAttachments());

    String sourceOrderCode = outBoundOrder.getSourceOrderCode();
    AllocateOrder allocateOrder = allocateOrderMapper.selectByCode(sourceOrderCode);
    AssertUtil.notNull(allocateOrder, ErrorCodeConstants.MATERIAL_DATA_NOT_EXISTS);
    List<InOutBoundOrderCreateVO.SourceOrderLine> sourceOrderLines = req.getSourceOrderLines();
    inBoundOrder.setSourceOrderCode(allocateOrder.getCode());
    inBoundOrder.setProjectId(allocateOrder.getTargetProjectId());

    outBoundOrder.setSourceOrderCode(allocateOrder.getCode());
    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    List<OutBoundOrderLine> outBoundOrderLines = Lists.newArrayList();

    Long projectId = outBoundOrder.getProjectId();
    Set<String> materialCodes = req.getSourceOrderLines().stream()
        .map(SourceOrderLine::getMaterialCode).collect(Collectors.toSet());
    List<ProjectStock> projectStocks = projectStockMapper.selectByProjectIdAndMaterialCodes(
        projectId, materialCodes);
    Map<String, ProjectStock> stockMap = projectStocks.stream()
        .collect(Collectors.toMap(ProjectStock::getMaterialCode, Function.identity()));

    List<ProjectStock> updateStock = new ArrayList<>();
    List<ProjectStockRecord> stockRecordInserts = new ArrayList<>();
    for (InOutBoundOrderCreateVO.SourceOrderLine sourceOrderLine : sourceOrderLines) {
      InBoundOrderLine inOutBoundOrderLine = new InBoundOrderLine();
      OutBoundOrderLine outBoundOrderLine = outBoundOrderLineMapper.selectById(
          sourceOrderLine.getId());
      String materialName = outBoundOrderLine.getMaterialName();

      BigDecimal outQty = sourceOrderLine.getOutQty();
      outBoundOrderLine.setRealQty(outQty);
      outBoundOrderLines.add(outBoundOrderLine);
      String materialCode = outBoundOrderLine.getMaterialCode();
      ProjectStock projectStock = stockMap.get(materialCode);
      if (Objects.isNull(projectStock)) {
        throw new ServiceException(500, materialName + "库存不存在");
      }
      if (projectStock.getAvailableQty().compareTo(outQty) < 0) {
        throw new ServiceException(500, materialName + "库存不足");
      }

      ProjectStockRecord stockRecord = new ProjectStockRecord();
      stockRecord.setProjectId(projectId);
      stockRecord.setMaterialCode(materialCode);
      stockRecord.setMaterialName(materialName);
      stockRecord.setAlterQty(outQty);

      stockRecord.setAlterType(5);
      stockRecord.setPrice(outBoundOrderLine.getPrice());
      stockRecord.setInOutBoundOrderCode(outBoundOrder.getCode());
      stockRecord.setRemark("调拨出库");
      stockRecordInserts.add(stockRecord);

      BigDecimal afterOutAvl = projectStock.getAvailableQty().subtract(outQty);
      projectStock.setAvailableQty(afterOutAvl);
      BigDecimal afterOutTotal = projectStock.getTotalQty().subtract(outQty);

      projectStock.setUsedQty(projectStock.getUsedQty().add(outQty));
      projectStock.setTotalQty(afterOutTotal);

      stockRecord.setAfterAvailableQty(afterOutAvl);
      stockRecord.setAfterTotalQty(afterOutTotal);
      stockRecord.setAfterAvailableQty(projectStock.getAvailableQty());

      updateStock.add(projectStock);

      inOutBoundOrderLine.setMaterialCode(materialCode);
      inOutBoundOrderLine.setMaterialName(materialName);
      inOutBoundOrderLine.setQty(outQty);
      inOutBoundOrderLine.setPrice(outBoundOrderLine.getPrice());
      inBoundOrderLines.add(inOutBoundOrderLine);

    }
    allocateOrder.setStatus(AllocateOrderStatusEnum.WAIT_IN.getStatus());
    transactionService.transaction(() -> {
      allocateOrderMapper.updateById(allocateOrder);
      inBoundOrderMapper.insert(inBoundOrder);
      outBoundOrderMapper.updateById(outBoundOrder);
      for (OutBoundOrderLine outBoundOrderLine : outBoundOrderLines) {
        outBoundOrderLineMapper.updateById(outBoundOrderLine);
      }
      projectStockMapper.updateBatch(updateStock, 1000);
      projectStockRecordMapper.insertBatch(stockRecordInserts);
      inBoundOrderLines.forEach(s -> s.setInOutBoundOrderId(inBoundOrder.getId()));
      inBoundOrderLineMapper.insertBatch(inBoundOrderLines);
      return null;
    });

  }

  @Override
  public OutBoundOrderVO getDetail(Long id) {
    OutBoundOrder outBoundOrder = outBoundOrderMapper.selectById(id);
    OutBoundOrderVO do2DetailVo = InOutBoundOrderConvert.INSTANCE.outBoundDo2DetailVo(
        outBoundOrder);
    List<OutBoundOrderLine> orderLineDOS = outBoundOrderLineMapper.selectByForeignKey(id);
    List<InOutBoundOrderLineVO> lineVOList = InOutBoundOrderConvert.INSTANCE.batchOutBoundLineDo2vo(
        orderLineDOS);

    for (InOutBoundOrderLineVO inOutBoundOrderLineVO : lineVOList) {
      setMaterial(inOutBoundOrderLineVO);
    }

    Integer sourceOrderType = outBoundOrder.getSourceOrderType();
    if (SourceOrderTypeEnum.ALLOCATE_ORDER.getType().equals(sourceOrderType)) {
      AllocateOrder allocateOrder = allocateOrderMapper.selectByCode(
          outBoundOrder.getSourceOrderCode());
      do2DetailVo.setTargetContactName(allocateOrder.getTargetContactName());
      do2DetailVo.setTargetProjectAddress(allocateOrder.getTargetProjectAddress());
      ProjectDO projectDO = projectMapper.selectById(allocateOrder.getTargetProjectId());
      do2DetailVo.setTargetProjectName(projectDO.getName());
    }

    do2DetailVo.setInOutBoundOrderLineVOList(lineVOList);

    ProjectDO projectDO = projectMapper.selectById(outBoundOrder.getProjectId());
    do2DetailVo.setProjectName(projectDO.getName());
    return do2DetailVo;
  }

  private void setMaterial(InOutBoundOrderLineVO inOutBoundOrderLineVO) {
    MaterialDO materialDO = materialMapper.selectOne(MaterialDO::getCode,
        inOutBoundOrderLineVO.getMaterialCode());
    inOutBoundOrderLineVO.setUnit(
        dictDataService.getLabelCache("materiel_unit", "" + materialDO.getMeasurementUnit()));
    inOutBoundOrderLineVO.setSettleUnitName(
        dictDataService.getLabelCache("materiel_unit", "" + inOutBoundOrderLineVO.getSettleUnit()));
    inOutBoundOrderLineVO.setSpecification(materialDO.getSpecification());
    inOutBoundOrderLineVO.setLength(materialDO.getLength());
    inOutBoundOrderLineVO.setWidth(materialDO.getWidth());
    inOutBoundOrderLineVO.setHeight(materialDO.getHeight());
    inOutBoundOrderLineVO.setProportion(materialDO.getProportion());
    inOutBoundOrderLineVO.setWidth(materialDO.getWidth());
    inOutBoundOrderLineVO.setMainImage(materialDO.getMainImage());
    inOutBoundOrderLineVO.setOtherImage(materialDO.getOtherImage());
    inOutBoundOrderLineVO.setMeasurementMethod(materialDO.getMeasurementMethod());
    inOutBoundOrderLineVO.setMeasurementUnit(materialDO.getMeasurementUnit());
    inOutBoundOrderLineVO.setType(materialDO.getType());
  }


  @Override
  @Transactional(rollbackFor = Exception.class)
  public void create(InOutBoundOrderCreateVO req) {
    List<InOutBoundOrderCreateVO.SourceOrderLine> sourceOrderLines = req.getSourceOrderLines();
    AssertUtil.notEmpty(sourceOrderLines, ErrorCodeConstants.INCORRECT_SHIPPED_QUANTITY);

    //发货数量必填，且大于0，结算数量必填
    for (SourceOrderLine sourceOrderLine : sourceOrderLines) {
      AssertUtil.isTrue(Objects.nonNull(sourceOrderLine.getDeliveryQty()) && (
              sourceOrderLine.getDeliveryQty().compareTo(BigDecimal.ZERO) > 0),
          ErrorCodeConstants.INCORRECT_SHIPPED_QUANTITY);

      AssertUtil.isTrue(Objects.nonNull(sourceOrderLine.getDeliverySettleQty()) && (
              sourceOrderLine.getDeliverySettleQty().compareTo(BigDecimal.ZERO) > 0),
          ErrorCodeConstants.INCORRECT_SHIPPED_SETTLE_QUANTITY);
    }

    //供应商发货，调拨单出库

    //生成供应商，调出方出库单，采购商，调入方入库单
    InBoundOrder inBoundOrder = new InBoundOrder();
    inBoundOrder.setCode(CodeGeneratorUtil.generateCode("IN"));
    inBoundOrder.setSourceOrderType(req.getSourceOrderType());
    inBoundOrder.setStatus(InOutBoundOrderStatusEnum.WAIT_IN.getStatus());
    inBoundOrder.setTruckNo(req.getTruckNo());
    inBoundOrder.setTruckDriverMobile(req.getTruckDriverMobile());
    inBoundOrder.setTruckDriverName(req.getTruckDriverName());
    inBoundOrder.setDeliveryRemark(req.getDeliveryRemark());

    OutBoundOrder outBoundOrder = new OutBoundOrder();
    outBoundOrder.setCode(CodeGeneratorUtil.generateCode("OUT"));
    outBoundOrder.setSourceOrderType(req.getSourceOrderType());
    outBoundOrder.setStatus(InOutBoundOrderStatusEnum.DONE.getStatus());
    outBoundOrder.setTruckNo(req.getTruckNo());
    outBoundOrder.setTruckDriverMobile(req.getTruckDriverMobile());
    outBoundOrder.setTruckDriverName(req.getTruckDriverName());

    if (SourceOrderTypeEnum.ALLOCATE_ORDER.getType().equals(req.getSourceOrderType())) {
      allot(req, sourceOrderLines, inBoundOrder, outBoundOrder);
      return;
    }
    sourceOrderLines = sourceOrderLines.stream().filter(s -> Objects.nonNull(s.getDeliveryQty()))
        .collect(Collectors.toList());
    AssertUtil.notEmpty(sourceOrderLines, ErrorCodeConstants.INCORRECT_SHIPPED_QUANTITY);
    Long sourceOrderId = req.getSourceOrderId();
    PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(sourceOrderId);

    AssertUtil.notNull(purchaseOrder, ErrorCodeConstants.MATERIAL_DATA_NOT_EXISTS);
    inBoundOrder.setContractCode(purchaseOrder.getContractNo());
    purchaseOrder.setStatus(PurchaseOrderStatusEnum.PART_OUT.getStatus());

    inBoundOrder.setSourceOrderCode(purchaseOrder.getCode());
    inBoundOrder.setProjectId(purchaseOrder.getProjectId());
    inBoundOrder.setSupplierId(purchaseOrder.getSupplierId());

    // 采购单结算规则为0，入库单直接结算
    String contractNo = purchaseOrder.getContractNo();
    if (StringUtils.hasLength(contractNo)) {
      ContractManager contractManager = contractManagerMapper.selectByCode(contractNo);
      // 采购单结算规则为0，入库单直接结算
      if (0 == contractManager.getPayRule()) {
        inBoundOrder.setSettled(true);
      }
    }

    outBoundOrder.setSourceOrderCode(purchaseOrder.getCode());
    outBoundOrder.setProjectId(purchaseOrder.getProjectId());

    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    List<PurchasePayLogDO> purchasePayLogDOS = Lists.newArrayList();

    List<JSONObject> hasOUtQty = inBoundOrderLineMapper.selectLineBySourceOrderCode(
        purchaseOrder.getCode());
    Map<String, BigDecimal> hasOutMap = Maps.newHashMap();
    hasOUtQty.forEach(s -> hasOutMap.put(s.getString("materialCode"), s.getBigDecimal("qty")));

    Map<String, BigDecimal> deliverySettleOutMap = Maps.newHashMap();
    hasOUtQty.forEach(s -> deliverySettleOutMap.put(s.getString("materialCode"),
        s.getBigDecimal("deliverySettleQty")));

    SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
        purchaseOrder.getSupplierId());

    Map<Long, SourceOrderLine> sourceOrderLineMap = sourceOrderLines.stream()
        .collect(Collectors.toMap(SourceOrderLine::getSourceOrderLneId, Function.identity()));

    List<PurchaseOrderLine> purchaseOrderLines = purchaseOrderLineMapper.selectBatchIds(
        sourceOrderLineMap.keySet());

    for (PurchaseOrderLine purchaseOrderLine : purchaseOrderLines) {
      InBoundOrderLine inOutBoundOrderLine = new InBoundOrderLine();
      String materialCode = purchaseOrderLine.getMaterialCode();
      inOutBoundOrderLine.setMaterialCode(materialCode);
      inOutBoundOrderLine.setMaterialName(purchaseOrderLine.getMaterialName());
      SourceOrderLine sourceOrderLine = sourceOrderLineMap.get(purchaseOrderLine.getId());
      BigDecimal deliveryQty = sourceOrderLine.getDeliveryQty();
      inOutBoundOrderLine.setQty(deliveryQty);
      inOutBoundOrderLine.setSettleUnit(purchaseOrderLine.getSettleUnit());
      inOutBoundOrderLine.setDeliverySettleQty(sourceOrderLine.getDeliverySettleQty());

      inOutBoundOrderLine.setPrice(purchaseOrderLine.getPrice());

      BigDecimal hasOut = hasOutMap.get(materialCode);
      if (Objects.isNull(hasOut)) {
        hasOutMap.put(materialCode, inOutBoundOrderLine.getQty());
      } else {
        hasOut = hasOut.add(inOutBoundOrderLine.getQty());
        hasOutMap.put(materialCode, hasOut);
      }
      inBoundOrderLines.add(inOutBoundOrderLine);
      BigDecimal outQty = purchaseOrderLine.getOutQty();
      purchaseOrderLine.setOutQty(outQty.add(deliveryQty));

      BigDecimal deliveryOut = deliverySettleOutMap.get(materialCode);
      BigDecimal deliverySettleQty = inOutBoundOrderLine.getDeliverySettleQty();
      if (Objects.isNull(deliveryOut)) {
        deliverySettleOutMap.put(materialCode, deliverySettleQty);
      } else {
        deliveryOut = deliveryOut.add(deliverySettleQty);
        deliverySettleOutMap.put(materialCode, deliveryOut);
      }
      BigDecimal outNewSettleQty = purchaseOrderLine.getOutNewSettleQty();
      purchaseOrderLine.setOutNewSettleQty(outNewSettleQty.add(deliverySettleQty));

    }

    List<JSONObject> sumQty = purchaseOrderLineMapper.getQty(sourceOrderId);
    Map<String, BigDecimal> sumQtyMap = Maps.newHashMap();
    sumQty.forEach(s -> sumQtyMap.put(s.getString("materialCode"), s.getBigDecimal("qty")));
    AtomicBoolean allOut = new AtomicBoolean(true);
    sumQtyMap.forEach((k, v) -> {
      BigDecimal out = hasOutMap.get(k);
      allOut.set(allOut.get() && Objects.nonNull(out) && out.compareTo(v) >= 0);
    });
    purchaseOrder.setStatus(allOut.get() ? PurchaseOrderStatusEnum.ALL_OUT.getStatus()
        : PurchaseOrderStatusEnum.PART_OUT.getStatus());
    transactionService.transaction(() -> {
      purchaseOrderMapper.updateById(purchaseOrder);
      inBoundOrderMapper.insert(inBoundOrder);
      outBoundOrderMapper.insert(outBoundOrder);
      inBoundOrderLines.forEach(s -> s.setInOutBoundOrderId(inBoundOrder.getId()));
      inBoundOrderLineMapper.insertBatch(inBoundOrderLines);
      purchaseOrderLineMapper.updateBatch(purchaseOrderLines, 1000);
      if (CollectionUtils.isNotEmpty(purchasePayLogDOS)) {
        purchasePayLogMapper.insertBatch(purchasePayLogDOS);
      }
      return null;
    });

    ProjectDO projectDO = projectMapper.selectById(purchaseOrder.getProjectId());
    employeeService.sendOrderDeliveryMsg(projectDO.getHeader(), req.getTruckDriverName(),
        req.getTruckDriverMobile(), inBoundOrder.getCode(), req.getTruckNo(),
        MiniProgramPageDict.IN_BOUND_ORDER + inBoundOrder.getId());
  }

  private void allot(InOutBoundOrderCreateVO req, List<SourceOrderLine> sourceOrderLines,
      InBoundOrder inBoundOrder, OutBoundOrder outBoundOrder) {
    Long sourceOrderId = req.getSourceOrderId();
    AllocateOrder allocateOrder = allocateOrderMapper.selectById(sourceOrderId);
    AssertUtil.notNull(allocateOrder, ErrorCodeConstants.MATERIAL_DATA_NOT_EXISTS);
    Map<Long, BigDecimal> collect = sourceOrderLines.stream().collect(
        Collectors.toMap(SourceOrderLine::getSourceOrderLneId, SourceOrderLine::getDeliveryQty));
    List<AllocateOrderLine> allocateOrderLines = allocateOrderLineMapper.selectBatchIds(
        collect.keySet());
    inBoundOrder.setSourceOrderCode(allocateOrder.getCode());
    inBoundOrder.setProjectId(allocateOrder.getTargetProjectId());

    outBoundOrder.setSourceOrderCode(allocateOrder.getCode());
    outBoundOrder.setProjectId(allocateOrder.getTargetProjectId());
    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    List<OutBoundOrderLine> outBoundOrderLines = Lists.newArrayList();
    for (AllocateOrderLine allocateOrderLine : allocateOrderLines) {
      InBoundOrderLine inOutBoundOrderLine = new InBoundOrderLine();
      inOutBoundOrderLine.setMaterialCode(allocateOrderLine.getMaterialCode());
      inOutBoundOrderLine.setMaterialName(allocateOrderLine.getMaterialName());
      inOutBoundOrderLine.setQty(collect.get(allocateOrderLine.getId()));
      inBoundOrderLines.add(inOutBoundOrderLine);

      OutBoundOrderLine outBoundOrderLine = new OutBoundOrderLine();
      outBoundOrderLine.setMaterialCode(allocateOrderLine.getMaterialCode());
      outBoundOrderLine.setMaterialName(allocateOrderLine.getMaterialName());
      outBoundOrderLine.setQty(collect.get(allocateOrderLine.getId()));
      outBoundOrderLines.add(outBoundOrderLine);
    }
    allocateOrder.setStatus(AllocateOrderStatusEnum.WAIT_IN.getStatus());
    transactionService.transaction(() -> {
      allocateOrderMapper.updateById(allocateOrder);
      inBoundOrderMapper.insert(inBoundOrder);
      outBoundOrderMapper.insert(outBoundOrder);
      inBoundOrderLines.forEach(s -> s.setInOutBoundOrderId(inBoundOrder.getId()));
      outBoundOrderLines.forEach(s -> s.setInOutBoundOrderId(outBoundOrder.getId()));
      inBoundOrderLineMapper.insertBatch(inBoundOrderLines);
      outBoundOrderLineMapper.insertBatch(outBoundOrderLines);
      return null;
    });
  }

}
