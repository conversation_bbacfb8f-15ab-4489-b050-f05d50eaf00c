package cn.hznanf.system.api.service.impl.contract;

import cn.hznanf.common.enums.ContractStatusEnum;
import cn.hznanf.common.enums.ContractType;
import cn.hznanf.common.enums.InOutBoundOrderStatusEnum;
import cn.hznanf.common.enums.PaymentStatus;
import cn.hznanf.common.enums.PurchaseOrderStatusEnum;
import cn.hznanf.common.enums.ReceiptOrderType;
import cn.hznanf.common.enums.SourceOrderTypeEnum;
import cn.hznanf.common.pojo.PageResult;
import cn.hznanf.common.util.object.AssertUtil;
import cn.hznanf.common.util.string.CodeGeneratorUtil;
import cn.hznanf.system.api.ErrorCodeConstants;
import cn.hznanf.system.api.RequestTimeHolder;
import cn.hznanf.system.api.WebFrameworkUtils;
import cn.hznanf.system.api.convert.ContractChangeRecordConvert;
import cn.hznanf.system.api.convert.ContractManagerConvert;
import cn.hznanf.system.api.convert.ContractPerformanceBondConvert;
import cn.hznanf.system.api.dataobject.Employee;
import cn.hznanf.system.api.dataobject.contract.ContractChangeRecord;
import cn.hznanf.system.api.dataobject.contract.ContractManager;
import cn.hznanf.system.api.dataobject.contract.ContractManagerLine;
import cn.hznanf.system.api.dataobject.contract.ContractPaymentPlans;
import cn.hznanf.system.api.dataobject.contract.ContractPerformanceBond;
import cn.hznanf.system.api.dataobject.finance.Payment;
import cn.hznanf.system.api.dataobject.material.MaterialDO;
import cn.hznanf.system.api.dataobject.project.ProjectDO;
import cn.hznanf.system.api.dataobject.project.ProjectReceiptOrder;
import cn.hznanf.system.api.dataobject.purchase.InBoundOrder;
import cn.hznanf.system.api.dataobject.purchase.InBoundOrderLine;
import cn.hznanf.system.api.dataobject.purchase.PurchaseOrder;
import cn.hznanf.system.api.dataobject.purchase.PurchaseOrderLine;
import cn.hznanf.system.api.dataobject.purchase.SupplierManagerDO;
import cn.hznanf.system.api.dict.ContractPerformanceBondStatus;
import cn.hznanf.system.api.dto.ContractChangeRecordDTO;
import cn.hznanf.system.api.dto.ReceiptOrderLineDTO;
import cn.hznanf.system.api.enums.PaymentType;
import cn.hznanf.system.api.service.LambdaQueryWrapperX;
import cn.hznanf.system.api.service.contract.ContractManagerService;
import cn.hznanf.system.api.service.finance.PaymentService;
import cn.hznanf.system.api.service.mapper.contract.ContractChangeRecordMapper;
import cn.hznanf.system.api.service.mapper.contract.ContractManagerLineMapper;
import cn.hznanf.system.api.service.mapper.contract.ContractManagerMapper;
import cn.hznanf.system.api.service.mapper.contract.ContractPaymentPlansMapper;
import cn.hznanf.system.api.service.mapper.contract.ContractPerformanceBondMapper;
import cn.hznanf.system.api.service.mapper.finance.PaymentMapper;
import cn.hznanf.system.api.service.mapper.material.MaterialMapper;
import cn.hznanf.system.api.service.mapper.project.ProjectMapper;
import cn.hznanf.system.api.service.mapper.project.ProjectReceiptOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.InBoundOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.InBoundOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseOrderLineMapper;
import cn.hznanf.system.api.service.mapper.purchase.PurchaseOrderMapper;
import cn.hznanf.system.api.service.mapper.purchase.SupplierManagerMapper;
import cn.hznanf.system.api.service.mapper.user.EmployeeMapper;
import cn.hznanf.system.api.service.transaction.TransactionService;
import cn.hznanf.system.api.vo.contract.ContractManagerCreateVO;
import cn.hznanf.system.api.vo.contract.ContractManagerLineVO;
import cn.hznanf.system.api.vo.contract.ContractManagerPageReqVO;
import cn.hznanf.system.api.vo.contract.ContractManagerVO;
import cn.hznanf.system.api.vo.finance.PaymentDTO;
import cn.hznanf.system.api.vo.finance.PaymentLineVO;
import cn.hznanf.system.api.vo.finance.PurchasePayDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * description 合同管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/10/28 21:58:53
 */
@Service
@Slf4j
public class ContractManagerServiceImpl implements ContractManagerService {

  @Resource
  private ContractManagerLineMapper contractManagerLineMapper;
  @Resource
  private ContractManagerMapper contractManagerMapper;

  @Resource
  private ContractPaymentPlansMapper contractPaymentPlansMapper;

  @Resource
  private SupplierManagerMapper supplierManagerMapper;

  @Resource
  private TransactionService transactionService;
  @Resource
  private EmployeeMapper employeeMapper;

  @Resource
  private MaterialMapper materialMapper;
  @Resource
  private ProjectMapper projectMapper;
  @Resource
  private PurchaseOrderMapper purchaseOrderMapper;
  @Resource
  private PaymentService paymentService;
  @Resource
  private PaymentMapper paymentMapper;
  @Resource
  private InBoundOrderMapper inBoundOrderMapper;
  @Resource
  private InBoundOrderLineMapper inBoundOrderLineMapper;
  @Resource
  private PurchaseOrderLineMapper purchaseOrderLineMapper;
  @Resource
  private ProjectReceiptOrderMapper projectReceiptOrderMapper;
  @Resource
  private ContractPerformanceBondMapper contractPerformanceBondMapper;
  @Resource
  private ContractChangeRecordMapper contractChangeRecordMapper;


  @Override
  public Long create(ContractManagerCreateVO req) {
    //采购合同校验输入和行汇总价
    ContractManager manager = ContractManagerConvert.INSTANCE.createVo2do(req);
    List<ContractManagerLine> lines = ContractManagerConvert.INSTANCE.batchLineVo2do(
        req.getContractManagerLineVOList());
    Set<String> collect = lines.stream().map(ContractManagerLine::getMaterialCode)
        .collect(Collectors.toSet());
    final Map<String, MaterialDO> materialDOMap = materialMapper.selectByCodes(collect).stream()
        .collect(Collectors.toMap(MaterialDO::getCode, Function.identity()));
    List<ContractPaymentPlans> plans = ContractManagerConvert.INSTANCE.batchPlanLineVo2do(
        req.getContractPaymentPlansVOList());
    manager.setCode(CodeGeneratorUtil.generateCode("CM"));
    manager.setStatus(ContractStatusEnum.UN_CONFIRM.getStatus());
    if (manager.getNakedTotalAmt() == null) {
      manager.setNakedTotalAmt(BigDecimal.ZERO);
    }
    return transactionService.transaction(() -> {
      contractManagerMapper.insert(manager);
      Long id = manager.getId();
      if (CollectionUtils.isNotEmpty(lines)) {
        lines.forEach(s -> {
          s.setContractManagerId(id);
          setLine(s, materialDOMap);
        });
        contractManagerLineMapper.insertBatch(lines);
      }

      if (CollectionUtils.isNotEmpty(req.getContractPerformanceBondVOList())) {
        List<ContractPerformanceBond> contractPerformanceBonds = ContractPerformanceBondConvert.INSTANCE.voToEntities(
            req.getContractPerformanceBondVOList());
        contractPerformanceBonds.forEach(s -> {
          s.setContractId(id);
          s.setCode(CodeGeneratorUtil.generateCode("CPB"));
          s.setContractType(manager.getType());
          s.setCustomerId(manager.getContractPartyId());
          s.setStatus(ContractPerformanceBondStatus.WAIT_EFFECT);
        });
        contractPerformanceBondMapper.insertBatch(contractPerformanceBonds);
      }

      if (CollectionUtils.isNotEmpty(plans)) {
        plans.forEach(s -> s.setContractManagerId(id));
        contractPaymentPlansMapper.insertBatch(plans);
      }
      return id;
    });
  }

  private static void setLine(ContractManagerLine s, Map<String, MaterialDO> materialDOMap) {
    MaterialDO materialDO = materialDOMap.get(s.getMaterialCode());
    if (Objects.nonNull(materialDO)) {
      s.setSpecification(materialDO.getSpecification());
    }
  }

  @Override
  public PageResult<ContractManagerVO> getPage(ContractManagerPageReqVO reqVO) {
    PageResult<ContractManager> pageResult = contractManagerMapper.selectPage(reqVO);
    final PageResult<ContractManagerVO> contractManagerVOPageResult = ContractManagerConvert.INSTANCE.do2vo(
        pageResult);
    final List<ContractManagerVO> list = contractManagerVOPageResult.getList();
    if (CollectionUtils.isEmpty(list)) {
      return contractManagerVOPageResult;
    }
    Set<Long> partIds = Sets.newHashSet();
    Set<Long> userIds = Sets.newHashSet();
    Set<Long> projectIds = Sets.newHashSet();
    Set<String> codes = Sets.newHashSet();
    for (ContractManagerVO contractManagerVO : list) {
      partIds.add(contractManagerVO.getContractPartyId());
      userIds.add(contractManagerVO.getHeader());
      userIds.add(contractManagerVO.getCreator());
      codes.add(contractManagerVO.getCode());
      if (Objects.nonNull(contractManagerVO.getProjectId())) {
        projectIds.add(contractManagerVO.getProjectId());
      }
    }
    List<Employee> employees = employeeMapper.selectBatchIds(userIds);
    Map<Long, String> userMap = employees.stream()
        .collect(Collectors.toMap(Employee::getId, Employee::getName));
    Map<Long, String> projectMap = Maps.newHashMap();
    if (CollectionUtils.isNotEmpty(projectIds)) {
      List<ProjectDO> projectDOS = projectMapper.selectBatchIds(projectIds);
      projectMap = projectDOS.stream()
          .collect(Collectors.toMap(ProjectDO::getId, ProjectDO::getName));
    }
    List<SupplierManagerDO> supplierManagerDOS = supplierManagerMapper.selectBatchIds(partIds);
    Map<Long, String> map = supplierManagerDOS.stream()
        .collect(Collectors.toMap(SupplierManagerDO::getId, SupplierManagerDO::getName));

    if (Integer.valueOf(0).equals(reqVO.getType())) {
      productContract(codes, list, map, userMap, projectMap);
    }
    if (Integer.valueOf(1).equals(reqVO.getType())) {

      purchaseContract(codes, list, map, userMap, projectMap);
    }
    return contractManagerVOPageResult;

  }

  private void purchaseContract(Set<String> codes, List<ContractManagerVO> list,
      Map<Long, String> supplierMap, Map<Long, String> userMap, Map<Long, String> projectMap) {
    List<PurchaseOrder> purchaseOrders = purchaseOrderMapper.selectByContractNos(codes);
    if (CollectionUtils.isEmpty(purchaseOrders)) {
      return;
    }

    Map<String, List<PurchaseOrder>> collect = purchaseOrders.stream()
        .collect(Collectors.groupingBy(PurchaseOrder::getContractNo));

    List<Payment> payments = paymentMapper.selectByContractNos(codes);

    List<String> purchaseOrderCodes = purchaseOrders.stream().map(PurchaseOrder::getCode)
        .collect(Collectors.toList());
    LambdaQueryWrapperX<InBoundOrder> queryWrapper = new LambdaQueryWrapperX<InBoundOrder>().in(
        InBoundOrder::getSourceOrderCode, purchaseOrderCodes);
    queryWrapper.eq(InBoundOrder::getStatus, InOutBoundOrderStatusEnum.DONE.getStatus());
    queryWrapper.eq(InBoundOrder::getSourceOrderType, SourceOrderTypeEnum.PURCHASE_ORDER.getType());
    List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectList(queryWrapper);

    Map<String, List<Long>> listMap = inBoundOrders.stream().collect(
        Collectors.groupingBy(InBoundOrder::getSourceOrderCode,
            Collectors.mapping(InBoundOrder::getId, Collectors.toList())));

    List<Long> inBoundOrderIds = inBoundOrders.stream().map(InBoundOrder::getId)
        .collect(Collectors.toList());
    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(inBoundOrderIds)) {
      inBoundOrderLines = inBoundOrderLineMapper.selectByForeignKeys(inBoundOrderIds);
    }
    Map<Long, List<InBoundOrderLine>> inBoundIdMap = inBoundOrderLines.stream()
        .collect(Collectors.groupingBy(InBoundOrderLine::getInOutBoundOrderId));

    Map<String, List<Payment>> paymentsGroup = payments.stream()
        .collect(Collectors.groupingBy(Payment::getContractNo));

    for (ContractManagerVO s : list) {
      s.setContractPartyName(supplierMap.get(s.getContractPartyId()));
      s.setHeaderName(userMap.get(s.getHeader()));
      s.setCreatorName(userMap.get(s.getCreator()));
      s.setProjectName(projectMap.get(s.getProjectId()));

      BigDecimal paidTotalAmt = BigDecimal.ZERO;
      BigDecimal waitPayAmt = BigDecimal.ZERO;
      BigDecimal waitSettleAmt = BigDecimal.ZERO;
      if (Integer.valueOf(1).equals(s.getPayRule())) {
        List<PurchaseOrder> orders = collect.get(s.getCode());
        if (CollectionUtils.isEmpty(orders)) {
          continue;
        }
        List<String> purchaseOrderCodeList = orders.stream().map(PurchaseOrder::getCode)
            .collect(Collectors.toList());
        //根据入库单算出的金额
        BigDecimal totalSettledAmt = BigDecimal.ZERO;
        BigDecimal totalPayedAmt = BigDecimal.ZERO;
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (String purchaseOrderCode : purchaseOrderCodeList) {
          List<Long> inBoundIdList = listMap.get(purchaseOrderCode);
          if (CollectionUtils.isEmpty(inBoundIdList)) {
            continue;
          }
          List<InBoundOrderLine> inBoundOrderLineList = inBoundIdList.stream()
              .map(inBoundIdMap::get).flatMap(List::stream).collect(Collectors.toList());

          totalSettledAmt = totalSettledAmt.add(
              inBoundOrderLineList.stream().map(InBoundOrderLine::getSettledAmt)
                  .reduce(BigDecimal.ZERO, BigDecimal::add));
          totalPayedAmt = totalPayedAmt.add(
              inBoundOrderLineList.stream().map(InBoundOrderLine::getPayedAmt)
                  .reduce(BigDecimal.ZERO, BigDecimal::add));
          totalAmt = totalAmt.add(inBoundOrderLineList.stream().map(InBoundOrderLine::getTotalAmt)
              .reduce(BigDecimal.ZERO, BigDecimal::add));

          paidTotalAmt = paidTotalAmt.add(totalPayedAmt);
          waitPayAmt = waitPayAmt.add(totalSettledAmt.subtract(paidTotalAmt));
          waitSettleAmt = waitSettleAmt.add(totalAmt.subtract(totalSettledAmt));
        }

      }

      List<Payment> paymentsResult = paymentsGroup.get(s.getCode());
      if (!CollectionUtils.isEmpty(paymentsResult)) {
        BigDecimal paymentPaidTotalAmount = paymentsResult.stream()
            .filter(o -> o.getStatus().equals(PaymentStatus.COMPLETE)).map(Payment::getPayAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        paidTotalAmt = paidTotalAmt.add(paymentPaidTotalAmount);

        BigDecimal paymentWaitPayAmt = paymentsResult.stream().filter(
                o -> !o.getStatus().equals(PaymentStatus.COMPLETE) && !o.getStatus()
                    .equals(PaymentStatus.REJECT)).map(Payment::getApplyAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        waitPayAmt = waitPayAmt.add(paymentWaitPayAmt);
      }
      s.setPaidTotalAmt(paidTotalAmt);
      s.setWaitPayAmt(waitPayAmt);
      s.setWaitSettleAmt(waitSettleAmt);
    }
  }

  private void productContract(Set<String> codes, List<ContractManagerVO> list,
      Map<Long, String> map, Map<Long, String> userMap, Map<Long, String> projectMap) {
    List<ProjectReceiptOrder> dbProjectReceiptOrders = projectReceiptOrderMapper.selectList(
        new LambdaQueryWrapperX<ProjectReceiptOrder>().in(ProjectReceiptOrder::getContractCode,
            codes).in(ProjectReceiptOrder::getStatus, Arrays.asList(0, 1, 2)));
    Map<String, List<ProjectReceiptOrder>> productCodesMap = dbProjectReceiptOrders.stream()
        .collect(Collectors.groupingBy(ProjectReceiptOrder::getContractCode));
    for (ContractManagerVO s : list) {
      s.setContractPartyName(map.get(s.getContractPartyId()));
      s.setHeaderName(userMap.get(s.getHeader()));
      s.setCreatorName(userMap.get(s.getCreator()));
      s.setProjectName(projectMap.get(s.getProjectId()));

      List<ProjectReceiptOrder> projectReceiptOrders = productCodesMap.get(s.getCode());
      if (CollectionUtils.isEmpty(projectReceiptOrders)) {
        s.setWaitReceiptAmt(s.getTotallyAmt());
        continue;
      }
      projectReceiptOrders = projectReceiptOrders.stream()
          .filter(o -> !ReceiptOrderType.CONTRACTPERFORMANCEBOND.equals(o.getType()))
          .collect(Collectors.toList());
      if (CollectionUtils.isEmpty(projectReceiptOrders)) {
        s.setWaitReceiptAmt(s.getTotallyAmt());
        continue;
      }
      BigDecimal waitReceiptAmt = projectReceiptOrders.stream()
          .filter(o -> Integer.valueOf(0).equals(o.getStatus()))
          .map(ProjectReceiptOrder::getReceiptAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal receiptedAmt = projectReceiptOrders.stream()
          .filter(o -> !Integer.valueOf(0).equals(o.getStatus()))
          .map(ProjectReceiptOrder::getReceiptAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
      s.setReceiptingAmt(waitReceiptAmt);
      s.setReceiptTotalAmt(receiptedAmt);
      s.setWaitReceiptAmt(s.getTotallyAmt().subtract(waitReceiptAmt).subtract(receiptedAmt));
    }
  }

  @Override
  public ContractManagerVO getDetail(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    ContractManagerVO do2DetailVo = ContractManagerConvert.INSTANCE.do2DetailVo(contractManager);
    List<ContractManagerLine> lines = contractManagerLineMapper.selectByContractManagerId(id);
    List<ContractPaymentPlans> paymentPlansList = contractPaymentPlansMapper.selectByContractManagerId(
        id);
    SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
        do2DetailVo.getContractPartyId());
    List<ContractManagerLineVO> contractManagerLineVOS = ContractManagerConvert.INSTANCE.batchLineDo2vo(
        lines);
    List<String> materialCodeList = contractManagerLineVOS.stream()
        .map(ContractManagerLineVO::getMaterialCode).collect(Collectors.toList());
    materialMapper.selectByCodes(materialCodeList).forEach(s -> {
      contractManagerLineVOS.stream().filter(o -> o.getMaterialCode().equals(s.getCode()))
          .forEach(o -> {
            o.setSpecification(s.getSpecification());
            o.setMeasurementUnit(s.getMeasurementUnit());
            o.setMainImage(s.getMainImage());
            o.setMeasurementUnit(s.getMeasurementUnit());
            o.setMaterialProp(s.getMaterialProp());
            o.setType(s.getType());
          });
    });

    do2DetailVo.setContractManagerLineVOList(contractManagerLineVOS);
    do2DetailVo.setContractPaymentPlansVOList(
        ContractManagerConvert.INSTANCE.batchPlanDo2vo(paymentPlansList));
    do2DetailVo.setContractPartyName(supplierManagerDO.getName());
    Set<Long> userIds = Sets.newHashSet();
    List<ContractChangeRecord> contractChangeRecords = Lists.newArrayList();
    if (ContractType.PRODUCT.equals(contractManager.getType())) {
      contractChangeRecords = contractChangeRecordMapper.selectList(
          new LambdaQueryWrapperX<ContractChangeRecord>().eq(ContractChangeRecord::getContractId,
              id));
      userIds.addAll(contractChangeRecords.stream().map(ContractChangeRecord::getCreator)
          .collect(Collectors.toList()));
    }

    userIds.add(do2DetailVo.getHeader());
    userIds.add(do2DetailVo.getCreator());
    if (Objects.nonNull(do2DetailVo.getConfirmId())) {
      userIds.add(do2DetailVo.getConfirmId());
    }
    if (Objects.nonNull(do2DetailVo.getInvalidId())) {
      userIds.add(do2DetailVo.getInvalidId());
    }
    if (Objects.nonNull(do2DetailVo.getHandler())) {
      userIds.add(do2DetailVo.getHandler());
    }
    userIds.add(do2DetailVo.getContractors());
    List<Employee> employees = employeeMapper.selectBatchIds(userIds);
    Map<Long, String> userMap = employees.stream()
        .collect(Collectors.toMap(Employee::getId, Employee::getName));
    do2DetailVo.setCreatorName(userMap.get(do2DetailVo.getCreator()));
    do2DetailVo.setHeaderName(userMap.get(do2DetailVo.getHeader()));
    do2DetailVo.setConfirmName(userMap.get(do2DetailVo.getConfirmId()));
    do2DetailVo.setInvalidName(userMap.get(do2DetailVo.getInvalidId()));
    do2DetailVo.setHandlerName(userMap.get(do2DetailVo.getHandler()));
    do2DetailVo.setContractorsName(userMap.get(do2DetailVo.getContractors()));
    if (Objects.nonNull(do2DetailVo.getProjectId())) {
      ProjectDO projectDO = projectMapper.selectById(do2DetailVo.getProjectId());
      do2DetailVo.setProjectName(projectDO.getName());
    }

    if (ContractType.PURCHASE.equals(contractManager.getType())) {

      setPurchaseOrder(do2DetailVo, contractManager, contractManagerLineVOS);

    } else {
      LambdaQueryWrapperX<ProjectReceiptOrder> queryWrapper = new LambdaQueryWrapperX<ProjectReceiptOrder>().eq(
              ProjectReceiptOrder::getContractCode, contractManager.getCode())
          .eq(ProjectReceiptOrder::getStatus, 2);
      List<ProjectReceiptOrder> projectReceiptOrders = projectReceiptOrderMapper.selectList(
          queryWrapper);
      if (CollectionUtils.isNotEmpty(projectReceiptOrders)) {
        if (ContractType.PRODUCT.equals(contractManager.getType())) {
          List<String> completedAttachments = projectReceiptOrders.stream()
              .filter(s -> CollectionUtils.isNotEmpty(s.getAttachments()))
              .map(ProjectReceiptOrder::getAttachments).flatMap(List::stream)
              .collect(Collectors.toList());
          do2DetailVo.setCompletedAttachments(completedAttachments);
        }
        List<ReceiptOrderLineDTO> orderLineDTOS = projectReceiptOrders.stream()
            .filter(o -> CollectionUtils.isNotEmpty(o.getReceiptOrderLineDTOList())).flatMap(
                projectReceiptOrder -> projectReceiptOrder.getReceiptOrderLineDTOList().stream())
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderLineDTOS)) {
          Map<String, BigDecimal> codeToTotalQty = Maps.newHashMap();
          for (ReceiptOrderLineDTO orderLineDTO : orderLineDTOS) {
            BigDecimal bigDecimal = codeToTotalQty.get(orderLineDTO.getCode());
            if (Objects.isNull(bigDecimal)) {
              codeToTotalQty.put(orderLineDTO.getCode(), orderLineDTO.getQty());
            } else {
              codeToTotalQty.put(orderLineDTO.getCode(), bigDecimal.add(orderLineDTO.getQty()));
            }
          }
          contractManagerLineVOS.forEach(s -> {
            BigDecimal totalQty = codeToTotalQty.get(s.getMaterialCode());
            s.setSettleQty(totalQty);
          });
        }
      }
    }

    if (Boolean.TRUE.equals(contractManager.getIsNeedGuarantees())
        && "CONTRACTPERFORMANCEBOND".equals(contractManager.getGuaranteeType())) {
      List<ContractPerformanceBond> contractPerformanceBonds = contractPerformanceBondMapper.selectList(
          new LambdaQueryWrapperX<ContractPerformanceBond>().eq(
              ContractPerformanceBond::getContractId, id));
      do2DetailVo.setContractPerformanceBondVOList(
          ContractPerformanceBondConvert.INSTANCE.toVOS(contractPerformanceBonds));
    }

    List<ContractChangeRecordDTO> dtos = ContractChangeRecordConvert.INSTANCE.toDTOS(
        contractChangeRecords);
    if (CollectionUtils.isNotEmpty(dtos)) {
      dtos.forEach(s -> {
        s.setCreatorName(userMap.get(s.getCreator()));
      });
    }
    do2DetailVo.setContractChangeRecordVOList(dtos);

    return do2DetailVo;
  }

  private void setPurchaseOrder(ContractManagerVO do2DetailVo, ContractManager contractManager,
      List<ContractManagerLineVO> contractManagerLineVOS) {

    BigDecimal paidTotalAmt = BigDecimal.ZERO;
    BigDecimal waitPayAmt = BigDecimal.ZERO;
    BigDecimal waitSettleAmt = BigDecimal.ZERO;
    BigDecimal totalSettledAmt = BigDecimal.ZERO;
    BigDecimal totalPayedAmt = BigDecimal.ZERO;
    BigDecimal totalAmt = BigDecimal.ZERO;

    List<PurchaseOrder> purchaseOrders = purchaseOrderMapper.selectByContractNo(
        do2DetailVo.getCode());
    //生成过付款单的
    Set<String> collectCode = purchaseOrders.stream().map(PurchaseOrder::getCode)
        .collect(Collectors.toSet());
    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(collectCode)) {
      List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectPurchase(collectCode);
      if (!CollectionUtils.isEmpty(inBoundOrders)) {
        Set<Long> collect = inBoundOrders.stream()
            .filter(s -> s.getStatus() == InOutBoundOrderStatusEnum.DONE.getStatus())
            .map(InBoundOrder::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(collect)) {
          inBoundOrderLines = inBoundOrderLineMapper.selectByForeignKeys(collect);
        }
      }
    }
    List<PurchaseOrderLine> purchaseOrderLines = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(purchaseOrders)) {
      Set<Long> collect = purchaseOrders.stream().map(PurchaseOrder::getId)
          .collect(Collectors.toSet());
      purchaseOrderLines = purchaseOrderLineMapper.selectListByPurchaseOrderIds(collect);

      if (Integer.valueOf(0).equals(contractManager.getPayRule())) {
        do2DetailVo.setWaitSettleAmt(BigDecimal.ZERO);
      } else {

        totalSettledAmt = totalSettledAmt.add(
            inBoundOrderLines.stream().map(InBoundOrderLine::getSettledAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        totalPayedAmt = totalPayedAmt.add(
            inBoundOrderLines.stream().map(InBoundOrderLine::getPayedAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        totalAmt = totalAmt.add(inBoundOrderLines.stream().map(InBoundOrderLine::getTotalAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        paidTotalAmt = paidTotalAmt.add(totalPayedAmt);
        waitPayAmt = waitPayAmt.add(totalSettledAmt.subtract(paidTotalAmt));
        waitSettleAmt = waitSettleAmt.add(totalAmt.subtract(totalSettledAmt));

      }
    }

    //预付场景的价格
    List<Payment> payments = paymentMapper.selectByContractNo(do2DetailVo.getCode());

    if (!CollectionUtils.isEmpty(payments)) {
      BigDecimal paymentPaidTotalAmount = payments.stream()
          .filter(o -> o.getStatus().equals(PaymentStatus.COMPLETE)).map(Payment::getPayAmt)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      paidTotalAmt = paidTotalAmt.add(paymentPaidTotalAmount);

      BigDecimal paymentWaitPayAmt = payments.stream().filter(
              o -> !o.getStatus().equals(PaymentStatus.COMPLETE) && !o.getStatus()
                  .equals(PaymentStatus.REJECT)).map(Payment::getApplyAmt)
          .reduce(BigDecimal.ZERO, BigDecimal::add);

      waitPayAmt = waitPayAmt.add(paymentWaitPayAmt);
    }
    do2DetailVo.setPaidTotalAmt(paidTotalAmt);
    do2DetailVo.setWaitPayAmt(waitPayAmt);
    do2DetailVo.setWaitSettleAmt(waitSettleAmt);


    for (ContractManagerLineVO contractManagerLineVO : contractManagerLineVOS) {
      BigDecimal inQty = inBoundOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(InBoundOrderLine::getRealQty).reduce(BigDecimal.ZERO, BigDecimal::add);

      BigDecimal newRealSettleQty = inBoundOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(InBoundOrderLine::getNewRealSettleQty).reduce(BigDecimal.ZERO, BigDecimal::add);

      BigDecimal purchaseQty = purchaseOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(PurchaseOrderLine::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

      BigDecimal purchaseSettleQty = purchaseOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(PurchaseOrderLine::getNewSettleQty).reduce(BigDecimal.ZERO, BigDecimal::add);

      contractManagerLineVO.setInQty(inQty);
      contractManagerLineVO.setInSettleQty(newRealSettleQty);
      contractManagerLineVO.setPurchaseQty(purchaseQty);
      contractManagerLineVO.setPurchaseSettleQty(purchaseSettleQty);
    }


  }


  @Override
  public void performanceStart(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    AssertUtil.isTrue(ContractStatusEnum.CONFIRMED.getStatus().equals(contractManager.getStatus()),
        ErrorCodeConstants.ONLY_CONFIRMED_ITEMS_ARE_ALLOWED_FOR_PERFORMANCE);
    contractManager.setStatus(ContractStatusEnum.PERFORMANCE.getStatus());
    contractManager.setHandlerTime(LocalDateTime.now());
    contractManager.setHandler(WebFrameworkUtils.getLoginUserId());
    contractManagerMapper.updateById(contractManager);
  }

  @Override
  public ContractManagerVO getByCode(String code, Long paymentId) {
    ContractManager contractManager = contractManagerMapper.selectByCode(code);
    ContractManagerVO do2DetailVo = ContractManagerConvert.INSTANCE.do2DetailVo(contractManager);
    List<ContractManagerLine> lines = contractManagerLineMapper.selectByContractManagerId(
        contractManager.getId());
    List<ContractPaymentPlans> paymentPlansList;
    if (Objects.nonNull(paymentId)) {
      Payment payment = paymentMapper.selectById(paymentId);
      Long planId = payment.getPlanId();
      paymentPlansList = Collections.singletonList(contractPaymentPlansMapper.selectById(planId));
    } else {
      paymentPlansList = contractPaymentPlansMapper.selectByContractManagerId(
          contractManager.getId());
    }
    SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
        do2DetailVo.getContractPartyId());
    List<ContractManagerLineVO> contractManagerLineVOS = ContractManagerConvert.INSTANCE.batchLineDo2vo(
        lines);
    do2DetailVo.setContractManagerLineVOList(contractManagerLineVOS);
    do2DetailVo.setContractPaymentPlansVOList(
        ContractManagerConvert.INSTANCE.batchPlanDo2vo(paymentPlansList));
    do2DetailVo.setContractPartyName(supplierManagerDO.getName());
    Employee headerUser = employeeMapper.selectById(do2DetailVo.getHeader());
    Employee createUser = employeeMapper.selectById(do2DetailVo.getCreator());
    do2DetailVo.setCreatorName(createUser.getName());
    do2DetailVo.setHeaderName(headerUser.getName());
    do2DetailVo.setRateAmt(do2DetailVo.getTotallyAmt().subtract(do2DetailVo.getNakedTotalAmt()));
    if (Objects.nonNull(do2DetailVo.getProjectId())) {
      ProjectDO projectDO = projectMapper.selectById(do2DetailVo.getProjectId());
      do2DetailVo.setProjectName(projectDO.getName());
    }
    List<Payment> payments = paymentMapper.selectByContractNo(do2DetailVo.getCode());
    do2DetailVo.setPaidTotalAmt(CollectionUtils.isEmpty(payments) ? BigDecimal.ZERO
        : payments.stream().filter(s -> s.getStatus().equals(PaymentStatus.COMPLETE))
            .map(Payment::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
    do2DetailVo.setWaitPayAmt(CollectionUtils.isEmpty(payments) ? BigDecimal.ZERO
        : payments.stream().filter(
                s -> !s.getStatus().equals(PaymentStatus.COMPLETE) && !s.getStatus()
                    .equals(PaymentStatus.REJECT)).map(Payment::getApplyAmt)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
    List<PurchaseOrder> purchaseOrders = purchaseOrderMapper.selectByContractNo(
        do2DetailVo.getCode());
    List<PurchaseOrderLine> purchaseOrderLines = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(purchaseOrders)) {
      Set<Long> collect = purchaseOrders.stream().map(PurchaseOrder::getId)
          .collect(Collectors.toSet());
      purchaseOrderLines = purchaseOrderLineMapper.selectListByPurchaseOrderIds(collect);
      Set<Long> unPayPurchase = purchaseOrders.stream().filter(
              s -> !s.isPayment() && !s.getStatus().equals(PurchaseOrderStatusEnum.DRAFT.getStatus())
                  && !s.getStatus().equals(PurchaseOrderStatusEnum.REFUSED.getStatus()))
          .map(PurchaseOrder::getId).collect(Collectors.toSet());
      do2DetailVo.setWaitSettleAmt(CollectionUtils.isNotEmpty(unPayPurchase)
          ? purchaseOrderLineMapper.selectListByPurchaseOrderIds(unPayPurchase).stream()
          .filter(s -> Objects.nonNull(s.getPrice())).map(s -> s.getPrice().multiply(s.getQty()))
          .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
    }
    Set<String> collectCode = purchaseOrders.stream().map(PurchaseOrder::getCode)
        .collect(Collectors.toSet());
    List<InBoundOrderLine> inBoundOrderLines = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(collectCode)) {
      List<InBoundOrder> inBoundOrders = inBoundOrderMapper.selectPurchase(collectCode);
      if (CollectionUtils.isNotEmpty(inBoundOrders)) {
        Set<Long> collect = inBoundOrders.stream()
            .filter(s -> s.getStatus() == InOutBoundOrderStatusEnum.DONE.getStatus())
            .map(InBoundOrder::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(collect)) {
          inBoundOrderLines = inBoundOrderLineMapper.selectByForeignKeys(collect);
        }
      }
    }

    List<String> materialCodeList = contractManagerLineVOS.stream()
        .map(ContractManagerLineVO::getMaterialCode).collect(Collectors.toList());
    materialMapper.selectByCodes(materialCodeList).forEach(s -> {
      contractManagerLineVOS.stream().filter(o -> o.getMaterialCode().equals(s.getCode()))
          .forEach(o -> {
            o.setSpecification(s.getSpecification());
            o.setUnit(s.getUnit());
            o.setMainImage(s.getMainImage());
            o.setMeasurementUnit(s.getMeasurementUnit());
            o.setMaterialProp(s.getMaterialProp());
            o.setType(s.getType());
          });
    });

    for (ContractManagerLineVO contractManagerLineVO : contractManagerLineVOS) {
      BigDecimal inQty = inBoundOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(InBoundOrderLine::getRealQty).reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal purchaseQty = purchaseOrderLines.stream()
          .filter(s -> s.getMaterialCode().equals(contractManagerLineVO.getMaterialCode()))
          .map(PurchaseOrderLine::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

      contractManagerLineVO.setInQty(inQty);
      contractManagerLineVO.setPurchaseQty(purchaseQty);
      contractManagerLineVO.setSettleQty(inQty);
    }
    return do2DetailVo;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Boolean addChangeRecord(ContractChangeRecordDTO req) {
    ContractChangeRecord contractChangeRecord = ContractChangeRecordConvert.INSTANCE.convertDTO2DO(
        req);
    contractChangeRecordMapper.insert(contractChangeRecord);

    ContractManager contractManager = contractManagerMapper.selectById(req.getContractId());
    if ("ADD".equals(req.getType())) {
      contractManager.setTotallyAmt(contractManager.getTotallyAmt().add(req.getAmt()));
//      contractManager.setNakedTotalAmt(contractManager.getNakedTotalAmt().add(req.getAmt()));
    } else {
      contractManager.setTotallyAmt(contractManager.getTotallyAmt().subtract(req.getAmt()));
//      contractManager.setNakedTotalAmt(contractManager.getNakedTotalAmt().subtract(req.getAmt()));
    }

    contractManagerMapper.updateById(contractManager);

    return Boolean.TRUE;
  }

  @Override
  public List<ContractManagerLineVO> getLineList(Long contractId) {
    List<ContractManagerLine> contractManagerLines = contractManagerLineMapper.selectByContractManagerId(
        contractId);
    List<ContractManagerLineVO> contractManagerLineVOS = ContractManagerConvert.INSTANCE.batchLineDo2vo(
        contractManagerLines);
    List<String> materialCodeList = contractManagerLineVOS.stream()
        .map(ContractManagerLineVO::getMaterialCode).collect(Collectors.toList());
    materialMapper.selectByCodes(materialCodeList).forEach(s -> {
      contractManagerLineVOS.stream().filter(o -> o.getMaterialCode().equals(s.getCode()))
          .forEach(o -> {
            o.setSpecification(s.getSpecification());
            o.setUnit(s.getUnit());
            o.setMainImage(s.getMainImage());
            o.setMeasurementUnit(s.getMeasurementUnit());
            o.setMaterialProp(s.getMaterialProp());
            o.setType(s.getType());
          });
    });
    return contractManagerLineVOS;
  }

  @Override
  public List<ContractManagerVO> simpleAllList() {
    List<ContractManager> contractManagers = contractManagerMapper.selectList(
        new LambdaQueryWrapperX<ContractManager>().select(ContractManager::getId,
                ContractManager::getCode, ContractManager::getName, ContractManager::getContractPartyId)
            .in(ContractManager::getStatus, ContractStatusEnum.CONFIRMED.getStatus(),
                ContractStatusEnum.PERFORMANCE.getStatus(), ContractStatusEnum.DONE.getStatus()));
    List<ContractManagerVO> contractManagerVOS = ContractManagerConvert.INSTANCE.batchDo2vo(
        contractManagers);

    return contractManagerVOS;
  }

  @Override
  public void createPayment(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    Integer payRule = contractManager.getPayRule();
    AssertUtil.isTrue(Integer.valueOf("1").equals(payRule),
        ErrorCodeConstants.ONLY_ALLOW_PAYMENT_RULES_FOR_PREPAYMENT_SIMULTANEOUS_PROCUREMENT_AND_PAYMENT_TYPE_PROCUREMENT_CONTRACT_OPERATIONS);
    List<PurchaseOrder> purchaseOrders = purchaseOrderMapper.selectByContractNoNotPayment(
        contractManager.getCode());
    AssertUtil.isTrue(CollectionUtils.isNotEmpty(purchaseOrders),
        ErrorCodeConstants.NO_DATA_NEEDS_TO_BE_GENERATED);
    SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
        contractManager.getContractPartyId());
    Set<Long> purchaseIds = purchaseOrders.stream().map(PurchaseOrder::getId)
        .collect(Collectors.toSet());
    List<PurchaseOrderLine> purchaseOrderLines = purchaseOrderLineMapper.selectListByPurchaseOrderIds(
        purchaseIds);

    BigDecimal total = purchaseOrderLines.stream().filter(s -> Objects.nonNull(s.getPrice()))
        .map(s -> s.getPrice().multiply(s.getQty())).reduce(BigDecimal.ZERO, BigDecimal::add);
    PaymentDTO paymentDTO = new PaymentDTO();
    paymentDTO.setTitle(contractManager.getName());
    paymentDTO.setApplyId(RequestTimeHolder.getLoginUser().getId());
    paymentDTO.setType(PaymentType.CONTRACT);
    paymentDTO.setContractNo(contractManager.getCode());
    paymentDTO.setBillAmt(total);
    paymentDTO.setDiscountAmt(new BigDecimal("0"));
    paymentDTO.setApplyAmt(total);
    paymentDTO.setPayeeName(supplierManagerDO.getName());
    paymentDTO.setPayeeType("BANK_PAY");
    paymentDTO.setRemark("");
    List<SupplierManagerDO.BankInfo> bankInfo = JSON.parseArray(
        JSON.toJSONString(supplierManagerDO.getBankInfo()), SupplierManagerDO.BankInfo.class);
    if (CollectionUtils.isNotEmpty(bankInfo)) {
      paymentDTO.setBankName(bankInfo.get(0).getBankName());
      paymentDTO.setBankCardNo(bankInfo.get(0).getBankAccount());
    }
    paymentDTO.setIsInvoice(false);
    paymentDTO.setPayAmt(new BigDecimal("0"));
    List<String> materialCodes = purchaseOrderLines.stream().map(PurchaseOrderLine::getMaterialCode)
        .collect(Collectors.toList());
    List<MaterialDO> materialDOS = materialMapper.selectByCodes(materialCodes);
    Map<String, MaterialDO> materialDOMap = materialDOS.stream()
        .collect(Collectors.toMap(MaterialDO::getCode, Function.identity()));
    Map<String, PaymentLineVO> paymentLineMap = Maps.newHashMap();
    for (PurchaseOrderLine purchaseOrderLine : purchaseOrderLines) {
      PaymentLineVO paymentLineVO = paymentLineMap.get(purchaseOrderLine.getMaterialCode());
      if (Objects.nonNull(paymentLineVO)) {
        paymentLineVO.getPurchaseDetail()
            .setQty(paymentLineVO.getPurchaseDetail().getQty().add(purchaseOrderLine.getQty()));
        continue;
      }
      MaterialDO materialDO = materialDOMap.get(purchaseOrderLine.getMaterialCode());
      PaymentLineVO paymentLine = new PaymentLineVO();
      PurchasePayDTO purchaseDetail = new PurchasePayDTO();
      purchaseDetail.setId(materialDO.getId());
      purchaseDetail.setCode(materialDO.getCode());
      purchaseDetail.setName(materialDO.getName());
      purchaseDetail.setQty(purchaseOrderLine.getQty());
      purchaseDetail.setPrice(purchaseOrderLine.getPrice());
      purchaseDetail.setUnit(materialDO.getUnit());
      purchaseDetail.setSpecification(materialDO.getSpecification());
      purchaseDetail.setLength(materialDO.getLength());
      purchaseDetail.setWidth(materialDO.getWidth());
      purchaseDetail.setHeight(materialDO.getHeight());
      purchaseDetail.setProportion(materialDO.getProportion());
      purchaseDetail.setWeight(materialDO.getWeight());
      purchaseDetail.setMainImage(materialDO.getMainImage());
      purchaseDetail.setRate(materialDO.getRate());
      purchaseDetail.setMeasurementUnit(materialDO.getMeasurementUnit());
      purchaseDetail.setType(materialDO.getType());
      paymentLine.setPurchaseDetail(purchaseDetail);
      paymentLineMap.put(materialDO.getCode(), paymentLine);
    }
    paymentDTO.setPaymentLineVOS(new ArrayList<>(paymentLineMap.values()));
    paymentDTO.setStatus(PaymentStatus.DRAFT);
    Payment payment = paymentService.addPayment(paymentDTO);
    purchaseOrders.forEach(s -> {
      s.setPayment(true);
      s.setPaymentCode(payment.getCode());
    });
    purchaseOrderMapper.updateBatch(purchaseOrders, 1000);
  }

  @Override
  public void performanceEnd(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    AssertUtil.isTrue(
        ContractStatusEnum.PERFORMANCE.getStatus().equals(contractManager.getStatus()),
        ErrorCodeConstants.ONLY_PERFORMANCE_ITEMS_ARE_ALLOWED_FOR_DONE);
    contractManager.setStatus(ContractStatusEnum.DONE.getStatus());
    contractManager.setDoneTime(LocalDateTime.now());
    contractManagerMapper.updateById(contractManager);
  }

  @Override
  public List<ContractManagerVO> simpleList(Integer type, Long projectId) {
//    List<ContractManager> contractManagers = contractManagerMapper.selectList(
//        new LambdaQueryWrapperX<ContractManager>().select(ContractManager::getId,
//                ContractManager::getTotallyAmt, ContractManager::getPayRule, ContractManager::getCode,
//                ContractManager::getName, ContractManager::getContractPartyId)
//            .in(ContractManager::getStatus, ContractStatusEnum.CONFIRMED.getStatus(),
//                ContractStatusEnum.PERFORMANCE.getStatus(), ContractStatusEnum.DONE.getStatus())
//            .eq(Objects.nonNull(type), ContractManager::getType, type)
//            .eq(Objects.nonNull(projectId), ContractManager::getProjectId, projectId));
    List<ContractManager> contractManagers = contractManagerMapper.selectByTypeAndProjectId(type,
        projectId);
    if (CollectionUtils.isEmpty(contractManagers)) {
      return Collections.emptyList();
    }
    List<ContractManagerVO> contractManagerVOS = ContractManagerConvert.INSTANCE.batchDo2vo(
        contractManagers);
    List<Long> contractSet = contractManagers.stream().map(ContractManager::getContractPartyId)
        .collect(Collectors.toList());
    List<SupplierManagerDO> supplierManagerDOS = supplierManagerMapper.selectBatchIds(contractSet);
    contractManagerVOS.forEach(s -> {
      SupplierManagerDO supplierManagerDO = supplierManagerDOS.stream()
          .filter(o -> o.getId().equals(s.getContractPartyId())).findFirst().orElse(null);
      if (Objects.nonNull(supplierManagerDO)) {
        s.setContractPartyName(supplierManagerDO.getName());
      }
    });

    if (Integer.valueOf(0).equals(type)) {
      Set<String> codes = contractManagerVOS.stream().map(ContractManagerVO::getCode)
          .collect(Collectors.toSet());
      Map<Long, String> userMap = Maps.newHashMap();
      Map<Long, String> projectMap = Maps.newHashMap();
      Map<Long, String> map = supplierManagerDOS.stream()
          .collect(Collectors.toMap(SupplierManagerDO::getId, SupplierManagerDO::getName));
      productContract(codes, contractManagerVOS, map, userMap, projectMap);
    }
    return contractManagerVOS;
  }

  @Override
  public void invalid(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    AssertUtil.isTrue(ContractStatusEnum.UN_CONFIRM.getStatus().equals(contractManager.getStatus()),
        ErrorCodeConstants.ONLY_UNCONFIRMED_ITEMS_ARE_ALLOWED_TO_BE_INVALIDATED);
    contractManager.setStatus(ContractStatusEnum.INVALID.getStatus());
    contractManager.setInvalidId(WebFrameworkUtils.getLoginUserId());
    contractManager.setInvalidTime(LocalDateTime.now());
    contractManagerMapper.updateById(contractManager);

    if (Boolean.TRUE.equals(contractManager.getIsNeedGuarantees())
        && "CONTRACTPERFORMANCEBOND".equals(contractManager.getGuaranteeType())) {
      List<ContractPerformanceBond> contractPerformanceBonds = contractPerformanceBondMapper.selectList(
          new LambdaQueryWrapperX<ContractPerformanceBond>().eq(
              ContractPerformanceBond::getContractId, id));
      if (CollectionUtils.isNotEmpty(contractPerformanceBonds)) {
        contractPerformanceBonds.forEach(s -> {
          s.setStatus(ContractPerformanceBondStatus.INVALID);
        });
        contractPerformanceBondMapper.updateBatch(contractPerformanceBonds, 1000);
      }
    }
  }

  @Override
  @Transactional
  public void confirm(Long id) {
    ContractManager contractManager = contractManagerMapper.selectById(id);
    AssertUtil.isTrue(ContractStatusEnum.UN_CONFIRM.getStatus().equals(contractManager.getStatus()),
        ErrorCodeConstants.ONLY_UNCONFIRMED_ITEMS_ARE_ALLOWED_FOR_CONFIRMATION);

    contractManager.setStatus(ContractStatusEnum.CONFIRMED.getStatus());
    contractManager.setConfirmId(WebFrameworkUtils.getLoginUserId());
    contractManager.setConfirmTime(LocalDateTime.now());
    contractManagerMapper.updateById(contractManager);

    //保证金状态修改
    if (Boolean.TRUE.equals(contractManager.getIsNeedGuarantees())
        && "CONTRACTPERFORMANCEBOND".equals(contractManager.getGuaranteeType())) {
      List<ContractPerformanceBond> contractPerformanceBonds = contractPerformanceBondMapper.selectList(
          new LambdaQueryWrapperX<ContractPerformanceBond>().eq(
              ContractPerformanceBond::getContractId, id));
      if (CollectionUtils.isNotEmpty(contractPerformanceBonds)) {
        if (contractManager.getType().equals(1)) {
          contractPerformanceBonds.forEach(s -> {
            s.setStatus(ContractPerformanceBondStatus.WAIT_RECEIPT);
          });
        } else {
          contractPerformanceBonds.forEach(s -> {
            s.setStatus(ContractPerformanceBondStatus.WAIT_PAY);
          });
        }
        contractPerformanceBondMapper.updateBatch(contractPerformanceBonds, 1000);
      }
    }

    if (Objects.isNull(contractManager.getPayRule()) || contractManager.getType().equals(0)) {
      return;
    }
    List<ContractPaymentPlans> contractPaymentPlans = contractPaymentPlansMapper.selectByContractManagerId(
        id);
    if (CollectionUtils.isEmpty(contractPaymentPlans)) {
      return;
    }
    List<ContractManagerLine> contractManagerLines = contractManagerLineMapper.selectByContractManagerId(
        id);
    Map<String, ContractManagerLine> managerLineMap = contractManagerLines.stream()
        .collect(Collectors.toMap(ContractManagerLine::getMaterialCode, Function.identity()));
    List<String> materialCodes = contractManagerLines.stream()
        .map(ContractManagerLine::getMaterialCode).collect(Collectors.toList());
    List<MaterialDO> materialDOS = materialMapper.selectByCodes(materialCodes);
    List<PaymentLineVO> collect = materialDOS.stream().map(s -> {
      PaymentLineVO paymentLineVO = new PaymentLineVO();
      PurchasePayDTO purchaseDetail = new PurchasePayDTO();
      purchaseDetail.setId(s.getId());
      purchaseDetail.setCode(s.getCode());
      purchaseDetail.setName(s.getName());
      purchaseDetail.setUnit(s.getUnit());
      purchaseDetail.setSpecification(s.getSpecification());
      purchaseDetail.setLength(s.getLength());
      purchaseDetail.setWidth(s.getWidth());
      purchaseDetail.setHeight(s.getHeight());
      purchaseDetail.setProportion(s.getProportion());
      purchaseDetail.setWeight(s.getWeight());
      purchaseDetail.setMainImage(s.getMainImage());
      purchaseDetail.setRate(s.getRate());
      ContractManagerLine contractManagerLine = managerLineMap.get(s.getCode());
      purchaseDetail.setPrice(contractManagerLine.getPrice());
      purchaseDetail.setQty(contractManagerLine.getQty());
      paymentLineVO.setPurchaseDetail(purchaseDetail);
      return paymentLineVO;
    }).collect(Collectors.toList());
    SupplierManagerDO supplierManagerDO = supplierManagerMapper.selectById(
        contractManager.getContractPartyId());
    for (ContractPaymentPlans contractPaymentPlan : contractPaymentPlans) {
      PaymentDTO paymentDTO = new PaymentDTO();
      paymentDTO.setTitle(contractManager.getName() + "-" + getDesc(contractPaymentPlan.getType()));
      paymentDTO.setApplyId(contractManager.getCreator());
      paymentDTO.setType(PaymentType.CONTRACT);
      paymentDTO.setProjectId(contractManager.getProjectId());
      paymentDTO.setContractNo(contractManager.getCode());
      paymentDTO.setBillAmt(contractPaymentPlan.getPaymentPlanAmt());
      paymentDTO.setDiscountAmt(new BigDecimal("0"));
      paymentDTO.setApplyAmt(contractPaymentPlan.getPaymentPlanAmt());
      paymentDTO.setPayeeName(supplierManagerDO.getName());
      paymentDTO.setPayeeType("BANK_PAY");
      paymentDTO.setRemark("自动生成");
      paymentDTO.setPlanId(contractPaymentPlan.getId());
      List<SupplierManagerDO.BankInfo> bankInfo = JSON.parseArray(
          JSON.toJSONString(supplierManagerDO.getBankInfo()), SupplierManagerDO.BankInfo.class);

      if (CollectionUtils.isNotEmpty(bankInfo)) {
        paymentDTO.setBankName(bankInfo.get(0).getBankName());
        paymentDTO.setBankCardNo(bankInfo.get(0).getBankAccount());
      }
      paymentDTO.setLastPayDate(contractPaymentPlan.getPaymentDeadline());
      paymentDTO.setIsInvoice(true);
      paymentDTO.setPayAmt(new BigDecimal("0"));
      paymentDTO.setPaymentLineVOS(collect);
      paymentDTO.setStatus(PaymentStatus.DRAFT);
      paymentService.addPayment(paymentDTO);
    }

  }

  private static String getDesc(Integer type) {
    switch (type) {
      case 0:
        return "预付款";
      case 1:
        return "普通";
      case 2:
        return "尾款";
      default:
        return "全款";
    }
  }


  @Override
  public void update(ContractManagerVO req) {
    Long id = req.getId();
    ContractManager contractManager = contractManagerMapper.selectById(id);
    AssertUtil.isTrue(ContractStatusEnum.UN_CONFIRM.getStatus().equals(contractManager.getStatus()),
        ErrorCodeConstants.ONLY_ALLOW_EDITING_OF_UNCONFIRMED_CONTRACTS);
    ContractManager manager = ContractManagerConvert.INSTANCE.vo2do(req);

    if (manager.getNakedTotalAmt() == null) {
      manager.setNakedTotalAmt(BigDecimal.ZERO);
    }

    List<ContractManagerLine> insert = Lists.newArrayList();

    List<ContractManagerLine> reqLines = ContractManagerConvert.INSTANCE.batchLineVo2do(
        req.getContractManagerLineVOList());
    List<ContractPaymentPlans> reqPaymentPlans = ContractManagerConvert.INSTANCE.batchPlanLineVo2do(
        req.getContractPaymentPlansVOList());

    if (CollectionUtils.isNotEmpty(reqLines)) {
      List<String> materialCodes = reqLines.stream().map(ContractManagerLine::getMaterialCode)
          .collect(Collectors.toList());
      List<MaterialDO> materialDOS = materialMapper.selectByCodes(materialCodes);
      Map<String, MaterialDO> materialDOMap = materialDOS.stream()
          .collect(Collectors.toMap(MaterialDO::getCode, Function.identity()));
      reqLines.forEach(s -> {
        MaterialDO materialDO = materialDOMap.get(s.getMaterialCode());
        if (Objects.nonNull(materialDO)) {
          s.setSpecification(materialDO.getSpecification());
        }
        s.setContractManagerId(id);
        s.setId(null);
        insert.add(s);
      });
    }

    transactionService.transaction(() -> {
      contractManagerMapper.updateById(manager);
      LambdaUpdateWrapper<ContractManagerLine> queryWrapper = new LambdaUpdateWrapper<>();
      queryWrapper.eq(ContractManagerLine::getContractManagerId, id);
      contractManagerLineMapper.delete(queryWrapper);
      if (CollectionUtils.isNotEmpty(insert)) {
        contractManagerLineMapper.insertBatch(insert);
      }

      if (CollectionUtils.isNotEmpty(reqPaymentPlans)) {
        reqPaymentPlans.forEach(s -> {
          if (Objects.isNull(s.getId())) {
            s.setContractManagerId(id);
            contractPaymentPlansMapper.insert(s);
            return;
          }
          contractPaymentPlansMapper.updateById(s);
        });
      }
      if (Boolean.TRUE.equals(manager.getIsNeedGuarantees()) && "CONTRACTPERFORMANCEBOND".equals(
          manager.getGuaranteeType())) {
        contractPerformanceBondMapper.delete(new LambdaQueryWrapperX<ContractPerformanceBond>().eq(
            ContractPerformanceBond::getContractId, id));
        if (CollectionUtils.isNotEmpty(req.getContractPerformanceBondVOList())) {
          List<ContractPerformanceBond> contractPerformanceBonds = ContractPerformanceBondConvert.INSTANCE.voToEntities(
              req.getContractPerformanceBondVOList());
          contractPerformanceBonds.forEach(s -> {
            s.setId(null);
            s.setContractId(id);
            if (Objects.isNull(s.getCode())) {
              s.setCode(CodeGeneratorUtil.generateCode("CPB"));
            }
            s.setContractType(contractManager.getType());
            s.setCustomerId(manager.getContractPartyId());
            s.setStatus(ContractPerformanceBondStatus.WAIT_EFFECT);
          });
          contractPerformanceBondMapper.insertBatch(contractPerformanceBonds);
        }
      }
      return true;
    });
  }

}
