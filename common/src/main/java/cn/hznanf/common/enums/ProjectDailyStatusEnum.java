package cn.hznanf.common.enums;

import cn.hznanf.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * description 项目日报状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@Getter
@AllArgsConstructor
public enum ProjectDailyStatusEnum implements IntArrayValuable {

    DRAFT(0, "草稿"),
    COMMITTED(1, "已提交"),
    COMMMENTED(2, "已批复")
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ProjectDailyStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
