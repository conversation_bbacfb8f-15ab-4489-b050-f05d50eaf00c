spring:
  application:
    name: app


  profiles:
    active: dev

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
#    throw-exception-if-no-handler-found: true # 404 错误时抛出异常，方便统一处理
#    static-path-pattern: /static/** # 静态资源路径; 注意：如果不配置，则 throw-exception-if-no-handler-found 不生效！！！ TODO 芋艿：不能配置，会导致 swagger 不生效

  # Jackson 配置项
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    disable-swagger-default-url: true
    enabled: true
    path: /swagger-ui

knife4j:
  enable: true
  setting:
    language: zh_cn

# 工作流 Flowable 配置
flowable:
  # 1. false: 默认值，Flowable 启动时，对比数据库表中保存的版本，如果不匹配。将抛出异常
  # 2. true: 启动时会对数据库中所有表进行更新操作，如果表存在，不做处理，反之，自动创建表
  # 3. create_drop: 启动时自动创建表，关闭时自动删除表
  # 4. drop_create: 启动时，删除旧表，再创建新表
  database-schema-update: true # 设置为 false，可通过 https://github.com/flowable/flowable-sql 初始化
  db-history-used: true # flowable6 默认 true 生成信息表，无需手动设置
  check-process-definitions: false # 设置为 false，禁用 /resources/processes 自动部署 BPMN XML 流程
  history-level: full # full：保存历史数据的最高级别，可保存全部流程相关细节，包括流程流转各节点参数

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${nfhg.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成


nfhg:
  info:
    version: 1.0.0
    base-package: cn.iocoder.nfhg
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/pay/notify/callback/* # 支付回调通知，不携带租户编号
      - /jmreport/* # 积木报表，无法携带租户编号
      - /actuator/*
  websocket:
    enable: true # websocket的开关
    path: /websocket/message # 路径
    maxOnlineCount: 0 # 最大连接人数
    sessionMap: true # 保存sessionMap
  captcha:
    enable: false # 验证码的开关，默认为 true
  codegen:
    base-package: ${nfhg.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  error-code: # 错误码相关配置项
    constants-class-list:
      - cn.hznanf.system.api.ErrorCodeConstants


debug: true

wx:
  appSecret: d080e47e2dc6eeacc726eff4863e3d37
  appId: wx97d31fb4135718e0

bank:
  verify:
    url: http://jumdres.market.alicloudapi.com/bankcard/4-validate
    appKey: *********
    appSecret: 98Jy89X0uj9IpS52xR7X1cMtrNboqkBO
    appCode: 453e11b5ddd04af7a615f38d4073667b

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  accessKeyId: LTAI5t5g8CLwHKrDCYuoo4PZ
  accessKeySecret: ******************************
  bucketName: hznfsb

