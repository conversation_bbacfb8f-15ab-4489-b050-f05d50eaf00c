package cn.hznanf.system.api.dataobject;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "employee_history_points", autoResultMap = true)
public class EmployeeHistoryPoints extends BaseDO {



    /**
     * 员工当前等级，整型
     */
    private Integer level;
    /**
     * 积分
     */
    private Integer points;

    /**
     * 当前安全上工总时长
     */
    private BigDecimal totalWorkTime;

    /**
     * 当月累计工时
     */
    private BigDecimal currentMonthTime;

    /**
     * 剩余没计算的工作时长
     */
    private BigDecimal leftWorkTime;


    private Long employeeId;


    private String employeeName;


    private LocalDateTime yearDate;

    private String employeeNo;



}
