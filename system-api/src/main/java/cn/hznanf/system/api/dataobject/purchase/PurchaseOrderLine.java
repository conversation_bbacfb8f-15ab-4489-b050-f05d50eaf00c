package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * description 采购单明细
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("purchase_order_line")
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderLine extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("采购单id")
    private Long purchaseOrderId;

    @ApiModelProperty("采购申请单行id")
    private Long purchaseApplyOrderLineId;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("采购数量")
    private BigDecimal qty;

    @ApiModelProperty("采购价格")
    private BigDecimal price;

    @ApiModelProperty("已发货数量")
    private BigDecimal outQty;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("结算数量")
    private BigDecimal newSettleQty;

    @ApiModelProperty("已发货结算数量")
    private BigDecimal outNewSettleQty;

    private Integer settleUnit;

}