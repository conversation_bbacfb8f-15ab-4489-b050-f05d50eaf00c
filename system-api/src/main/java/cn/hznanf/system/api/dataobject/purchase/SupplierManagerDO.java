package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import cn.hznanf.system.api.ObjectTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * description 供应商管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName(value = "supplier_manager", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierManagerDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商状态
     */
    private int status;

    /**
     * 类型，0供应商，1客户
     */
    private Integer type;


    /**
     * 备注
     */
    private String remark;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 组织机构类型
     */
    private Integer orgType;


    /**
     * 主营业务
     */
    private Integer mainBusiness;

    /**
     * 法人代表
     */
    private String legalRepresentative;

    /**
     * 企业类型
     */
    private Integer enterpriseType;

    /**
     * 是否三证合一
     */
    private boolean threeCertificatesInOne;

    /**
     * 营业执照号
     */
    private String businessLicenseNum;


    /**
     * 组织机构代码
     */
    private String orgCode;


    /**
     * 注册资本，单位：万元
     */
    private BigDecimal registeredCapital;

    /**
     * 实缴资本，单位：万元
     */
    private BigDecimal paidUpCapital;

    /**
     * 成立日期
     */
    private LocalDate establishmentDay;

    /**
     * 营业期限开始时间
     */
    private LocalDateTime businessStartTime;

    /**
     * 营业期限结束时间
     */
    private LocalDateTime businessEndTime;

    /**
     * 经营状态,0异常，1正常
     */
    private Integer businessStatus;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 公司官网
     */
    private String officialWebsite;

    /**
     * 公司电话
     */
    private String enterprisePhone;


    /**
     * 公司邮箱
     */
    private String enterpriseEmail;

    /**
     * 公司地址
     */
    private String enterpriseAddress;

    /**
     * 供应商关联的账号id
     */
    private Long relatedEmployeeId;


    @TableField(typeHandler = ObjectTypeHandler.class)
    private List<Contact> contacts;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<BankInfo> bankInfo;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;


    @Data
    public static class Contact {
        /**
         * 供应商联系人
         */
        private String contactName;

        /**
         * 供应商联系人电话
         */
        private String contactPhone;

        /**
         * 供应商联系人邮箱
         */
        private String contactEmail;

        /**
         * 供应商联系人职务
         */
        private String contactPost;

        /**
         * 是否主联系人
         */
        private boolean main;
    }


    @Data
    public static class BankInfo {
        /**
         * 银行名称
         */
        private String bankName;

        /**
         * 银行账户
         */
        private String bankAccount;


        /**
         * 持卡人姓名
         */
        private String userName;
    }
//
//    @Data
//    public static class Attachment {
//        /**
//         * 附件名称
//         */
//        private String name;
//
//        /**
//         * 附件地址
//         */
//        private String url;
//
//    }
}