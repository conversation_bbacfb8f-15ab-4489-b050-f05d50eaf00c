package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * description 供应商供货关系
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("supplier_material_relationship")
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierMaterialRelationshipDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 备注
     */
    private String remark;
}