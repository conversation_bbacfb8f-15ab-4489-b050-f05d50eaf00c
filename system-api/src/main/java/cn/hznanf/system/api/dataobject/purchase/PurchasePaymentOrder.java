package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * description 采购付账
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName(value = "purchase_payment_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchasePaymentOrder extends BaseDO {


    @ApiModelProperty("款项标题")
    private String name;


    @ApiModelProperty("付款单号")
    private String code;

    @ApiModelProperty("付款单状态 0未付款1部分付款，2全部付完")
    private Integer status;


    @ApiModelProperty("关联对账单号")
    private String billCode;


    @ApiModelProperty("应付总金额")
    private BigDecimal payAmt;


    @ApiModelProperty("实付总金额")
    private BigDecimal paidAmt;


    @ApiModelProperty("付款时间")
    private LocalDateTime payTime;


    @ApiModelProperty("付款方式，0 现金支付，1银行转账，2支付宝、3微信，4支票")
    private Integer payWay;


    @ApiModelProperty("供应商id")
    private Long supplierId;


    @ApiModelProperty("附件")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;


    @ApiModelProperty("备注")
    private String remark;
}