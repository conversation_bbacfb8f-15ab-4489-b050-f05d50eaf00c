package cn.hznanf.system.api.vo.finance;

import cn.hznanf.system.api.enums.PaymentType;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class PaymentTypeConverter implements Converter<String> {

  public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
      GlobalConfiguration globalConfiguration) {
    if (PaymentType.PERSONAL_TRANSFER.equals(value)) {
      return new WriteCellData<>("个人转账");
    } else if (PaymentType.REFUND.equals(value)) {
      return new WriteCellData<>("备用金申请");
    } else if (PaymentType.SALARY.equals(value)) {
      return new WriteCellData<>("工资支付");
    } else if (PaymentType.INSURANCE.equals(value)) {
      return new WriteCellData<>("保险支付");
    } else if (PaymentType.CATERING.equals(value)) {
      return new WriteCellData<>("餐饮支付");
    } else if (PaymentType.OTHER.equals(value)) {
      return new WriteCellData<>("其它");
    } else if (PaymentType.EQUIPMENT_RENTAL.equals(value)) {
      return new WriteCellData<>("设备租赁");
    } else if (PaymentType.PROCUREMENT.equals(value)) {
      return new WriteCellData<>("采购支付");
    } else if (PaymentType.CONTRACT.equals(value)) {
      return new WriteCellData<>("合同支付");
    } else if (PaymentType.CONTRACT_PROCUREMENT.equals(value)) {
      return new WriteCellData<>("合同采购支付");
    } else if (PaymentType.CONTRACTPERFORMANCEBOND.equals(value)) {
      return new WriteCellData<>("合同履约保证金");
    }

    return null;
  }

}
