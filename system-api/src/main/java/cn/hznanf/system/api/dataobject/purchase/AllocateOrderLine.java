package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * description 调拨单明细
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("allocate_order_line")
@Data
@EqualsAndHashCode(callSuper = true)
public class AllocateOrderLine extends BaseDO {

    private static final long serialVersionUID = 1L;
    /**
     * 调拨单id
     */
    @ApiModelProperty("调拨单id")
    private Long allocateOrderId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 物料id
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 调拨数量
     */
    @ApiModelProperty("调拨数量")
    private BigDecimal qty;

    @ApiModelProperty("单价")
    private BigDecimal price;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}