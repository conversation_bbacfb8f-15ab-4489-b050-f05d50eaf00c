package cn.hznanf.system.api.dataobject;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * description 技能证书
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@Data
@Accessors(chain = true)
@TableName(value = "employee_skill", autoResultMap = true)
public class EmployeeSkill extends BaseDO {
    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 获取时间
     */
    private LocalDate getTime;

    /**
     * 失效时间
     */
    private LocalDate invalidTime;

    /**
     * 是否长期有效
     */
    private Boolean longTermEffective;

    /**
     * 附件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;

    /**
     * 备注
     */
    private String remark;


}