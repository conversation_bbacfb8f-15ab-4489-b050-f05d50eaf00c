package cn.hznanf.system.api;


import cn.hznanf.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

  // ========== AUTH 模块 1002000000 ==========
  ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1002000000, "登录失败，账号密码不正确");
  ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1002000001, "登录失败，账号被禁用");
  ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1002000004, "验证码不正确，原因：{}");
  ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1002000005, "未绑定账号，需要进行绑定");
  ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1002000006, "Token 已经过期");
  ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1002000007, "手机号不存在");

  // ========== 菜单模块 1002001000 ==========
  ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1002001000, "已经存在该名字的菜单");
  ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1002001001, "父菜单不存在");
  ErrorCode MENU_PARENT_ERROR = new ErrorCode(1002001002, "不能设置自己为父菜单");
  ErrorCode MENU_NOT_EXISTS = new ErrorCode(1002001003, "菜单不存在");
  ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1002001004, "存在子菜单，无法删除");
  ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1002001005,
      "父菜单的类型必须是目录或者菜单");

  // ========== 角色模块 1002002000 ==========
  ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1002002000, "角色不存在");
  ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1002002001, "已经存在名为【{}】的角色");
  ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1002002002, "已经存在编码为【{}】的角色");
  ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1002002003,
      "不能操作类型为系统内置的角色");
  ErrorCode ROLE_IS_DISABLE = new ErrorCode(1002002004, "名字为【{}】的角色已被禁用");
  ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1002002005, "编码【{}】不能使用");

  ErrorCode USER_RELATED_NOT_ALLOWED_DEL = new ErrorCode(1002002006,
      "存在用户关联，请先解除授权关系再进行操作");

  ErrorCode POST_RELATED_NOT_ALLOWED_DEL = new ErrorCode(1002002006,
      "存在岗位关联，请先解除授权关系再进行操作");

  // ========== 用户模块 1002003000 ==========
  ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1002003000, "用户账号已经存在");
  ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1002003001, "手机号已经存在");
  ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1002003002, "邮箱已经存在");
  ErrorCode USER_NOT_EXISTS = new ErrorCode(1002003003, "用户不存在");
  ErrorCode USER_IS_ENABLE = new ErrorCode(1002003009, "用户状态为启用，不能删除");
  ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1002003004, "导入用户数据不能为空！");
  ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1002003005, "用户密码校验失败");
  ErrorCode USER_IS_DISABLE = new ErrorCode(1002003006, "名字为【{}】的用户已被禁用");
  ErrorCode USER_COUNT_MAX = new ErrorCode(1002003008,
      "创建用户失败，原因：超过租户最大租户配额({})！");

  ErrorCode USER_RELATED_BY_PROJECT = new ErrorCode(1002003009,
      "该员工为项目负责人，请在项目管理解绑后再操作");

  // ========== 部门模块 1002004000 ==========
  ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1002004000, "已经存在该名字的部门");
  ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1002004001, "父级部门不存在");
  ErrorCode DEPT_NOT_FOUND = new ErrorCode(1002004002, "当前部门不存在");
  ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1002004003, "存在子部门，无法删除");
  ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1002004004, "不能设置自己为父部门");
  ErrorCode DEPT_EXISTS_USER = new ErrorCode(1002004005, "部门中存在员工，无法删除");
  ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1002004006, "部门({})不处于开启状态，不允许选择");
  ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1002004007, "不能设置自己的子部门为父部门");

  ErrorCode DEPT_STATUS_IS_ENABLED = new ErrorCode(1002004008, "启用状态不允许删除");

  ErrorCode DEPT_EXITS_CHILDREN_NOT_ALLOWED_DISABLED = new ErrorCode(1002004003,
      "存在启用的子部门，请先停用子部门");

  // ========== 岗位模块 1002005000 ==========
  ErrorCode POST_NOT_FOUND = new ErrorCode(1002005000, "当前岗位不存在");
  ErrorCode POST_NOT_ENABLE = new ErrorCode(1002005001, "岗位({}) 不处于开启状态，不允许选择");
  ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1002005002, "已经存在该名字的岗位");
  ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1002005003, "已经存在该标识的岗位");
  ErrorCode AUTHORIZED_ROLE_PARAMETER_EXCEPTION = new ErrorCode(1002005004, "授权的角色参数异常");

  ErrorCode RELATED_EXIST = new ErrorCode(1002005005, "岗位被员工关联，不允许删除");

  ErrorCode ONLY_DISABLED_POST_CAN_BE_DEL = new ErrorCode(1002005006, "只有关闭的岗位才可以删除");

  ErrorCode RELATED_EXIST_NOT_ALLOWED_DISABLED = new ErrorCode(1002005007,
      "岗位被员工关联，不允许关闭");

  // ========== 字典类型 1002006000 ==========
  ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1002006001, "当前字典类型不存在");
  ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1002006002, "字典类型不处于开启状态，不允许选择");
  ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1002006003, "已经存在该名字的字典类型");
  ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1002006004, "已经存在该类型的字典类型");
  ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1002006005, "无法删除，该字典类型还有字典数据");

  // ========== 字典数据 1002007000 ==========
  ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1002007001, "当前字典数据不存在");
  ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1002007002,
      "字典数据({})不处于开启状态，不允许选择");
  ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1002007003, "已经存在该值的字典数据");

  // ========== 通知公告 1002008000 ==========
  ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1002008001, "当前通知公告不存在");

  // ========== 短信渠道 1002011000 ==========
  ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1002011000, "短信渠道不存在");
  ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1002011001, "短信渠道不处于开启状态，不允许选择");
  ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1002011002, "无法删除，该短信渠道还有短信模板");

  // ========== 短信模板 1002012000 ==========
  ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1002012000, "短信模板不存在");
  ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1002012001, "已经存在编码为【{}】的短信模板");

  // ========== 短信发送 1002013000 ==========
  ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1002013000, "手机号不存在");
  ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1002013001, "模板参数({})缺失");
  ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1002013002, "短信模板不存在");

  // ========== 短信验证码 1002014000 ==========
  ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1002014000, "验证码不存在");
  ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1002014001, "验证码已过期");
  ErrorCode SMS_CODE_USED = new ErrorCode(1002014002, "验证码已使用");
  ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode(1002014003, "验证码不正确");
  ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1002014004,
      "超过每日短信发送数量");
  ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1002014005, "短信发送过于频率");
  ErrorCode SMS_CODE_IS_EXISTS = new ErrorCode(1002014006, "手机号已被使用");
  ErrorCode SMS_CODE_IS_UNUSED = new ErrorCode(1002014007, "验证码未被使用");

  // ========== 租户信息 1002015000 ==========
  ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1002015000, "租户不存在");
  ErrorCode TENANT_DISABLE = new ErrorCode(1002015001, "名字为【{}】的租户已被禁用");
  ErrorCode TENANT_EXPIRE = new ErrorCode(1002015002, "名字为【{}】的租户已过期");
  ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1002015003,
      "系统租户不能进行修改、删除等操作！");
  ErrorCode TENANT_NAME_DUPLICATE = new ErrorCode(1002015004, "名字为【{}】的租户已存在");

  // ========== 租户套餐 1002016000 ==========
  ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1002016000, "租户套餐不存在");
  ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1002016001,
      "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除");
  ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1002016002, "名字为【{}】的租户套餐已被禁用");

  // ========== 错误码模块 1002017000 ==========
  ErrorCode ERROR_CODE_NOT_EXISTS = new ErrorCode(1002017000, "错误码不存在");
  ErrorCode ERROR_CODE_DUPLICATE = new ErrorCode(1002017001, "已经存在编码为【{}】的错误码");

  // ========== 社交用户 1002018000 ==========
  ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1002018000, "社交授权失败，原因是：{}");
  ErrorCode SOCIAL_USER_UNBIND_NOT_SELF = new ErrorCode(1002018001, "社交解绑失败，非当前用户绑定");
  ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1002018002, "社交授权失败，找不到对应的用户");

  // ========== 系统敏感词 1002019000 =========
  ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode(1002019000, "系统敏感词在所有标签中都不存在");
  ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode(1002019001, "系统敏感词已在标签中存在");

  // ========== OAuth2 客户端 1002020000 =========
  ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1002020000, "OAuth2 客户端不存在");
  ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1002020001, "OAuth2 客户端编号已存在");
  ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1002020002, "OAuth2 客户端已禁用");
  ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1002020003,
      "不支持该授权类型");
  ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1002020004, "授权范围过大");
  ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1002020005,
      "无效 redirect_uri: {}");
  ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1002020006, "无效 client_secret: {}");

  // ========== OAuth2 授权 1002021000 =========
  ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1002021000, "client_id 不匹配");
  ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(10********, "redirect_uri 不匹配");
  ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(**********, "state 不匹配");
  ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");

  // ========== OAuth2 授权 ********** =========
  ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");
  ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(**********, "code 已过期");

  // ========== 邮箱账号 ********** ==========
  ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "邮箱账号不存在");
  ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode(**********,
      "无法删除，该邮箱账号还有邮件模板");

  // ========== 邮件模版 ********** ==========
  ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "邮件模版不存在");
  ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(**********, "邮件模版 code({}) 已存在");

  // ========== 邮件发送 ********** ==========
  ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(**********, "模板参数({})缺失");
  ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(**********, "邮箱不存在");

  // ========== 站内信模版 ********** ==========
  ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "站内信模版不存在");
  ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE = new ErrorCode(**********,
      "已经存在编码为【{}】的站内信模板");

  // ========== 站内信模版 ********** ==========

  // ========== 站内信发送 1002028000 ==========
  ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(**********, "模板参数({})缺失");


  // ========== 项目管理 1002029000==========
  ErrorCode PROJECT_DATA_NOT_EXISTS = new ErrorCode(1002029000, "当前项目数据不存在");

  ErrorCode DELETION_IS_NOT_ALLOWED = new ErrorCode(1002029001, "只有未提交的数据才可删除");

  ErrorCode PROJECT_SCHEDULE_DATA_NOT_EXISTS = new ErrorCode(1002029002, "项目进度数据不存在");

  ErrorCode PROJECT_DAILY_DATA_NOT_EXISTS = new ErrorCode(1002029003, "项目日报数据不存在");

  ErrorCode PROJECT_DAILY_DATA_MUST_BE_DRAFT_TO_COMMIT = new ErrorCode(1002029004,
      "只有草稿的日报才可提交");
  ErrorCode PROJECT_DAILY_DATA_MUST_BE_DRAFT_TO_EDIT = new ErrorCode(1002029005,
      "只有草稿的日报才可修改");

  ErrorCode PROJECT_DAILY_DATA_EXISTS = new ErrorCode(1002029006, "该项目今天已经填过了");


  ErrorCode PROJECT_HAS_COMMITTED_SCHEDULE_DELETION_IS_NOT_ALLOWED = new ErrorCode(1002029006,
      "项目已提交，不允许删除节点");
  // ========== 打卡点 1002030000==========
  ErrorCode PROJECT_ASSOCIATION = new ErrorCode(1002030000, "存在项目关联，无法删除");

  // ==========采购 1002031000==========
  ErrorCode PURCHASE_APPLY_ORDER_DATA_NOT_EXISTS = new ErrorCode(1002031000,
      "采购申请单数据不存在");
  ErrorCode UN_COMMITTED_CAN_BE_ALLOWED = new ErrorCode(1002031001, "只允许操作未提交的");
  ErrorCode SAME_MATERIAL = new ErrorCode(1002031003, "存在相同物料");
  ErrorCode WAIT_AUDIT_CAN_BE_ALLOWED = new ErrorCode(1002031002, "只允许操作待审核");
  ErrorCode ONLY_MATERIALS_WITHIN_THE_CONTRACT_CAN_BE_SELECTED = new ErrorCode(1002031005,
      "只能选择合同内的物料");

  ErrorCode THE_PURCHASER_HAS_NOT_CONFIRMED = new ErrorCode(1002031003, "采购商未确认");

  ErrorCode NO_SETTLEMENT_DATA_TO_GENERATE = new ErrorCode(1002031004, "没有可生成的结算数据");

  ErrorCode THE_PAYMENT_ORDER_HAS_BEEN_COMPLETED_AND_DOES_NOT_NEED_TO_BE_REPEATED = new ErrorCode(
      1002031005, "付款单已完成付款，无需重复");
  ErrorCode ONLY_ALLOW_THE_SAME_SUPPLIER = new ErrorCode(1002031006, "只允许相同供应商进行操作");
  ErrorCode ONLY_OPERATIONS_WITH_A_STATUS_OF_COMPLETED_ARE_ALLOWED = new ErrorCode(1002031007,
      "只允许状态为完结的进行操作");
  ErrorCode THERE_ARE_SETTLED_RECEIPT_ORDERS_PLEASE_FILTER_AND_OPERATE_AGAIN = new ErrorCode(
      1002031008, "存在已结算的入库单，请筛选后重新操作");
  ErrorCode UNABLE_TO_FIND_RELEVANT_RECORDS = new ErrorCode(1002031009, "查询不到相关记录");
  ErrorCode INCORRECT_SHIPPED_QUANTITY = new ErrorCode(1002031010, "发货数量不正确");
  ErrorCode INCORRECT_SHIPPED_SETTLE_QUANTITY = new ErrorCode(1002031010, "发货结算数量不正确");


  // ==========供应商 1002032000==========
  ErrorCode SUPPLIER_DATA_NOT_EXISTS = new ErrorCode(1002032000, "数据不存在");
  ErrorCode SUPPLIER_STATUS_IS_ENABLED = new ErrorCode(1002032100, "启用状态不允许删除");

  ErrorCode SUPPLIER_SAME_NAME = new ErrorCode(1002032200, "已存在相同名称的供应商");

  ErrorCode THE_PHONE_NUMBER_HAS_ALREADY_BEEN_BOUND_TO_ANOTHER_SUPPLIER = new ErrorCode(1002032201,
      "手机号已经和其他供应商绑定");


  // ==========物料 1002033000==========
  ErrorCode MATERIAL_DATA_NOT_EXISTS = new ErrorCode(1002033000, "数据不存在");


  // =========库存 1002034000==========
  ErrorCode STOCK_DATA_NOT_EXISTS = new ErrorCode(1002034000, "库存数据不存在");

  ErrorCode NON_PROJECT_MEMBERS_CANNOT_CLAIM = new ErrorCode(1002034100, "非项目成员不能申领");

  ErrorCode INSUFFICIENT_INVENTORY = new ErrorCode(1002034200, "库存不足");

  // =========调拨单 1002035000==========
  ErrorCode PURCHASE_APPLY_ORDER_NOT_AGREE = new ErrorCode(1002035000,
      "采购申请单未审核通过，不予许发起调拨");
  ErrorCode PURCHASE_APPLY_LINE_HAS_BEEN_DEAL = new ErrorCode(1002035100,
      "采购申请单行已被处理，刷新后重试");
  ErrorCode ALLOCATE_ORDER_MUST_UN_CONFIRM = new ErrorCode(1002035200, "只允许操作状态为未确认的");
  ErrorCode PROJECT_STOCK_NOT_ENOUGH = new ErrorCode(1002035300, "调出项目库存不足");
  ErrorCode ONLY_WAIT_IN_CAN_BE_IN = new ErrorCode(1002035400, "必须为待入库的单据才能入库");

  // =========合同 1002036000==========
  ErrorCode ONLY_UNCONFIRMED_ITEMS_ARE_ALLOWED_TO_BE_INVALIDATED = new ErrorCode(1002036000,
      "只允许未确认的进行作废");
  ErrorCode ONLY_UNCONFIRMED_ITEMS_ARE_ALLOWED_FOR_CONFIRMATION = new ErrorCode(1002036001,
      "只允许未确认的进行确认");
  ErrorCode ONLY_ALLOW_EDITING_OF_UNCONFIRMED_CONTRACTS = new ErrorCode(1002036002,
      "只允许编辑待确认的合同");
  ErrorCode ONLY_CONFIRMED_ITEMS_ARE_ALLOWED_FOR_PERFORMANCE = new ErrorCode(1002036001,
      "只允许已确认的进行履约");
  ErrorCode ONLY_PERFORMANCE_ITEMS_ARE_ALLOWED_FOR_DONE = new ErrorCode(1002036002,
      "只允许进行中的进行完成");
  ErrorCode ONLY_ALLOW_PAYMENT_RULES_FOR_PREPAYMENT_SIMULTANEOUS_PROCUREMENT_AND_PAYMENT_TYPE_PROCUREMENT_CONTRACT_OPERATIONS = new ErrorCode(
      1002036003, "只允许付款规则为预付边采边付型采购合同操作");
  ErrorCode NO_DATA_NEEDS_TO_BE_GENERATED = new ErrorCode(**********, "没有需要生成的数据");
  ErrorCode THE_TOTAL_PRICE_INCLUDING_TAX_IS_NOT_EQUAL_TO_THE_TOTAL_MATERIAL_EXCHANGE_PRICE = new ErrorCode(
      **********, "含税总价与物料汇总价不相等");
  ErrorCode THE_TOTAL_PRICE_EXCLUDING_TAX_IS_NOT_EQUAL_TO_THE_TOTAL_MATERIAL_EXCHANGE_PRICE = new ErrorCode(
      **********, "不含税总价与物料汇总价不相等");


  // =========财务 **********==========
  ErrorCode THE_INVOICE_HAS_ALREADY_BEEN_ISSUED_AND_CANNOT_BE_INVALIDATED = new ErrorCode(
      **********, "已经开票，不能作废");
  ErrorCode INVOICED_ALREADY_NO_NEED_TO_REPEAT = new ErrorCode(**********, "已经开票，无需重复");
  ErrorCode INSUFFICIENT_BALANCE_UNABLE_TO_DEDUCT = new ErrorCode(**********, "余额不足，无法扣减");
  ErrorCode A_FUND_ACCOUNT_ALREADY_EXISTS_UNDER_THE_USERNAME = new ErrorCode(**********,
      "用户名下已存在资金账户");
  ErrorCode YOUR_REQUEST_HAS_BEEN_COMPLETED = new ErrorCode(**********, "您的请求已经完成");

  ErrorCode YOU_CANT_HANDLE_YOUR_REQ = new ErrorCode(**********, "您不能处理自己提交的申请");


  ErrorCode IN_OUT_BOUND_ORDER_LINE_EMPTY = new ErrorCode(**********, "请至少填一项物料");
  ErrorCode IN_QTY_MUST_GREATER_THAN_ZERO = new ErrorCode(**********, "入库数量必须大于0");
  ErrorCode IN_SETTLE_QTY_MUST_GREATER_THAN_ZERO = new ErrorCode(**********,
      "入库结算数量必须大于0");
  ErrorCode PURCHASE_QTY_NOT_NULL = new ErrorCode(**********, "采购数量不能为空");

  ErrorCode PURCHASE_SETTLE_QTY_NOT_NULL = new ErrorCode(**********, "采购结算数量不能为空");
  ErrorCode PURCHASE_PRICE_NOT_NULL = new ErrorCode(**********, "采购单价不能为空");
}
