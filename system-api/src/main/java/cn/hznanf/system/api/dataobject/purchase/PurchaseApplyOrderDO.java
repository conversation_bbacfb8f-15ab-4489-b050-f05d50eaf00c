package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import cn.hznanf.system.api.vo.AddressVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * description 采购申请单
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName(value = "purchase_apply_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseApplyOrderDO extends BaseDO {

    /**
     * 采购申请单单号
     */
    private String code;

    /**
     * 采购归属项目
     */
    private Long projectId;

    /**
     * 采购归属项目名称
     */
    private String projectName;

    /**
     * 最晚的采购时间
     */
    private LocalDateTime latestPurchaseTime;

    /**
     * 期望送达时间
     */
    private LocalDate expectArriveTime;


    /**
     * 申请单状态，0草稿，1待审核，2审核通过，3审核拒绝,4完成
     */
    private Integer status;


    /**
     * 申请人
     */
    private Long applicant;

    /**
     * 审核人
     */
    private Long auditor;

    /**
     * 拒绝原因
     */
    private String refusalReason;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 备注
     */
    private String remark;

    @Schema(description = "采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate purchaseDate;

    @Schema(description = "采购收货地址")
    private String receiveAddress;

    @Schema(description = "采购方联系方式")
    private String receiveContactPhone;

    @Schema(description = "采购方联系人")
    private String receiveContactPerson;
//
//    private Integer relateOrderType;
//
//    private String relateOrderNo;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachments;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private AddressVO addressVO;


}