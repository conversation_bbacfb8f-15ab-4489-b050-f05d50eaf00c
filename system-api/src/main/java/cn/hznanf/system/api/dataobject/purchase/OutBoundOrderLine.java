package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * description 出入库单明细
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("out_bound_order_line")
@Data
@EqualsAndHashCode(callSuper = true)
public class OutBoundOrderLine extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("出入库单号id")
    private Long inOutBoundOrderId;


    @ApiModelProperty("物料编码")
    private String materialCode;


    @ApiModelProperty("物料名称")
    private String materialName;


    @ApiModelProperty("出库数量")
    private BigDecimal qty;

    @ApiModelProperty("实际出库数量")
    private BigDecimal realQty;

    @ApiModelProperty("价格")
    private BigDecimal price;


    @ApiModelProperty("备注")
    private String remark;

}