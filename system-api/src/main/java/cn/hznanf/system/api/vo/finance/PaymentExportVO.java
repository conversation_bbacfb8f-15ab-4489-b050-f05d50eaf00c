package cn.hznanf.system.api.vo.finance;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
public class PaymentExportVO implements java.io.Serializable {

  private static final long serialVersionUID = 1L;

  @ExcelProperty("付款单编号")
  private String code;

  @ExcelProperty("付款单名称")
  private String title;

  @ExcelProperty("费用归属项目")
  private String projectName;

  @ApiModelProperty("个人转账-PERSONAL_TRANSFER，备用金申请-REFUND,工资支付-SALARY，保险支付INSURANCE，餐饮支付CATERING，其它OTHER，设备租赁EQUIPMENT_RENTAL，采购支付 PROCUREMENT")
  @ExcelProperty(value = "付款单类型",converter = PaymentTypeConverter.class)
  private String type;

  @ApiModelProperty("状态，草稿-DRAFT，待审批-WAIT_APPROVAL，审批驳回-REJECT，待付款-WAIT_PAY，完成-COMPLETE")
  @ExcelProperty(value = "付款单状态", converter = PaymentStatusConverter.class)
  private String status;

  @ExcelProperty("最晚付款时间")
  private LocalDate lastPayDate;

  @ExcelProperty("申请金额（元）")
  private BigDecimal applyAmt;

  @ExcelProperty("实际付款金额（元）")
  private BigDecimal payAmt;

  @ExcelProperty("收款对象")
  private String payeeName;

  @ExcelProperty(value = "是否包含发票", converter = BooleanValueConverter.class)
  private Boolean isInvoice;

  @ExcelProperty(value = "收款方式", converter = PayeeTypeConverter.class)
  private String payeeType;

  @ExcelProperty("合同编号")
  private String contractNo;

  @ExcelProperty("申请人")
  private String applyName;

  @ExcelProperty("申请时间")
  private LocalDateTime applyTime;

  @ExcelProperty("付款单备注")
  private String remark;

  @ExcelProperty("审核人")
  private String auditName;

  @ExcelProperty("审核时间")
  private LocalDateTime auditTime;

  @ExcelProperty("创建人")
  private String createName;

}

