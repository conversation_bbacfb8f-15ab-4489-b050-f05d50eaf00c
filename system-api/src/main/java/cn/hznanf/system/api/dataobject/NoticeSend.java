package cn.hznanf.system.api.dataobject;


import cn.hznanf.common.mybatis.type.JsonLongListTypeHandler;
import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 薪资计算任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Getter
@Setter
@TableName(value = "notice_send", autoResultMap = true)
@ApiModel(value = "微信消息发送", description = "微信消息发送")
public class NoticeSend extends BaseDO {

    private static final long serialVersionUID = 1L;


    @TableField("status")
    private String status;

    @TableField("type")
    private String type;

    @TableField(typeHandler = JsonLongListTypeHandler.class)
    private List<Long> employeeIds;

    private String path;

    private String templateId;


}

