package cn.hznanf.system.api.dataobject.permission;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * description 岗位角色
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("system_post_role")
@Data
@EqualsAndHashCode(callSuper = true)
public class PostRoleDO extends BaseDO {

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 角色id
     */
    private Long roleId;
}