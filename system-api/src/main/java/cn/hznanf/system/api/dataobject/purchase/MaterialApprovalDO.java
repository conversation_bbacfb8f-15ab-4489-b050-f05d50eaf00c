package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * description 用料审批单
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("material_approval")
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialApprovalDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用料审批单号
     */
    private String code;

    /**
     * 关联采购单号
     */
    private Long purchaseOrderId;

    /**
     * 关联采购单号编号
     */
    private String purchaseOrderCode;

    /**
     * 状态，1待审核，2审核通过，3审核拒绝
     */
    private int status;

    /**
     * 审核人
     */
    private Long auditor;

    /**
     * 拒绝原因
     */
    private String refusalReason;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 申请人ID
     */
//    private Long applyId;
//
//    /**
//     *
//     */
//    private Long projectId;

}