package cn.hznanf.system.api;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.List;

public class JsonLongSerializer extends JsonSerializer<List<Long>> {
    @Override
    public void serialize(List<Long> values, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (values != null) {
            long[] array = new long[values.size()];
            for (int i = 0; i < values.size(); i++) {
                final Object value = values.get(i);
                if (value instanceof Long) {
                    array[i] = (long) value;
                } else {
                    array[i] = ((Integer) value).longValue();
                }
            }
            gen.writeArray(array, 0, values.size());
        }
    }
}