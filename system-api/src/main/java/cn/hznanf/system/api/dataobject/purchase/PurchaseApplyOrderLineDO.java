package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * description 采购申请单物料
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("purchase_apply_order_line")
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseApplyOrderLineDO extends BaseDO {

    /**
     * 采购申请单id
     */
    private Long purchaseApplyOrderId;

    /**
     * 采购物料编码
     */
    private String materialCode;

    /**
     * 采购物料名称
     */
    private String materialName;

    /**
     * 采购物料数量
     */
    private BigDecimal qty;

    /**
     * 采购物料数量
     */
    private BigDecimal dealQty;

    /**
     * 物料单位
     */
    private String unit;

    /**
     * 是否已处理，生成了采购单或者调拨单，则为已处理
     */
    private boolean deal;

    private String remark;

    private BigDecimal price;
}