package cn.hznanf.system.api.dataobject;


import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 薪资计算任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("todo")
@ApiModel(value = "Todo对象", description = "薪资计算任务")
public class Todo extends BaseDO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("状态，WAIT_PROCESS-待处理，PROCESS-已处理")
    @TableField("status")
    private String status;

    @ApiModelProperty("记录id")
    @TableField("record_id")
    private Long recordId;

    @ApiModelProperty("类型：ATTENDANCE-打卡，CLAIM-报销，RESERVE_FUND-备用金")
    @TableField("type")
    private String type;

    @ApiModelProperty("标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("完成时间")
    @TableField("complete_time")
    private LocalDateTime completeTime;

    private Long applyId;

    private Long dealId;

}

