package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * description 项目库存流水
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName("project_stock_record")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectStockRecord extends BaseDO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("项目id")
    private Long projectId;


    @ApiModelProperty("物料编码")
    private String materialCode;


    @ApiModelProperty("物料名称")
    private String materialName;


    @ApiModelProperty("变动库存数量")
    private BigDecimal alterQty;


    @ApiModelProperty("变动后可用库存数量")
    private BigDecimal afterAvailableQty;


    @ApiModelProperty("变动后总库存数量")
    private BigDecimal afterTotalQty;

    @ApiModelProperty("采购或者调拨时的单价，还有库存维护")
    private BigDecimal price;


    @ApiModelProperty("变动类型，0：采购入库，1：使用消耗,2:盘点增加，3盘点减少，4:调拨入库,5:调拨出库")
    private Integer alterType;

    @ApiModelProperty("关联的出入库单号")
    private String inOutBoundOrderCode;


    @ApiModelProperty("remark")
    private String remark;

}