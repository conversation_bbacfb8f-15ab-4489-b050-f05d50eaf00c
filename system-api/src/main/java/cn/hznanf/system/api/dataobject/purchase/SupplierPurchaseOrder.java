package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * description 供应商采购单
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName(value = "supplier_purchase_order", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierPurchaseOrder extends BaseDO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("采购单号")
    private String code;

    @ApiModelProperty("采购所属项目")
    private Long projectId;

    @ApiModelProperty("采购申请单id")
    private Long purchaseApplyOrderId;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("采购单类型，0零星，1大件")
    private Integer type;

    @TableField(typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty("附件")
    private List<String> attachments;

    @ApiModelProperty("采购合同")
    private String contractNo;

    @ApiModelProperty("采购单状态")
    private Integer status;

    @NotNull(message = "采购单类型不能为空")
    @Schema(description = "申请单类型,NEW 自主创建，APPLY 申请创建")
    private String purchaseType;

    @Schema(description = "采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate purchaseDate;

    @Schema(description = "采购收货地址")
    private String receiveAddress;

    @Schema(description = "采购方联系方式")
    private String receiveContactPhone;

    @NotNull(message = "最晚的采购时间不能为空")
    @Schema(description = "最晚的采购时间")
    private LocalDateTime latestPurchaseTime;

    @NotNull(message = "期望送达时间不能为空")
    @Schema(description = "期望送达时间")
    private LocalDate expectArriveTime;

    @Schema(description = "供应商收货地址")
    private String sendAddress;

    @Schema(description = "供应商收货地址")
    private String sendContactPhone;

    @ApiModelProperty("备注")
    private String remark;

    @Schema(description = "采购方联系人")
    private String receiveContactPerson;

    @Schema(description = "供应商方联系人")
    private String sendContactPerson;

}