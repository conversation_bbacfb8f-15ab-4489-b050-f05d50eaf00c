package cn.hznanf.system.api.dataobject.purchase;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * description 采购申请单关联单据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/28 21:58:53
 */
@TableName(value = "purchase_apply_order_related", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseApplyOrderRelatedDO extends BaseDO {

    /**
     * 采购申请单id
     */
    private Long purchaseApplyOrderId;

    /**
     *关联单号
     */
    private String relateOrderNo;

    /**
     * 关联单据类型
     */
    private int relateOrderType;
}