package cn.hznanf.system.api;

import cn.hznanf.system.api.dto.LoginUser;

public class RequestTimeHolder {
    private static final ThreadLocal<LoginUser> startTimeThreadLocal = ThreadLocal.withInitial(() -> new LoginUser());

    public static void setLoginUser(LoginUser startTime) {
        startTimeThreadLocal.set(startTime);
    }

    public static LoginUser getLoginUser() {
        return startTimeThreadLocal.get();
    }

    public static void clear() {
        startTimeThreadLocal.remove();
    }
}
