package cn.hznanf.system.api.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
@Data
@TableName(value = "employee_attendance_detail",autoResultMap = true)
public class EmployeeAttendanceDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID，主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 普通打卡开始时间
     */
    private LocalTime punchStart;
    /**
     * 普通打卡结束时间
     */
    private LocalTime punchEnd;

    /**
     * 打卡时长
     */
    private BigDecimal punchDuration;


    /**
     * 加班打卡班次ID
     */
    private Long workShiftId;

    private LocalDateTime createAt;

    /**
     * 打卡记录id
     */
    private Long attendanceId;

    /**
     * 审批id
     */
    private Long approvalId;

    /**
     * 审核状态
     */
    private String approvalStatus;

    /**
     * 打卡类型，正常打卡，外勤打卡，补卡
     */
    private String attendanceType;

    /**
     * 员工id
     */
    private Long employeeId;

    private Double latitude;

    private Double longitude;

    private Long punchPointId;

    private LocalDate punchDate;

    private Long projectId;

    private String reason;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> picUrl;

}

