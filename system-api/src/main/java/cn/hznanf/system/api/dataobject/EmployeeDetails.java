package cn.hznanf.system.api.dataobject;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-02 11:03:56
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "employee_details",autoResultMap = true)
public class EmployeeDetails extends BaseDO {

    private static final long serialVersionUID = 1L;


    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 员工身份证号码
     */
    private String idCard;

    /**
     * 员工银行卡号码
     */
    private String bankCard;

    /**
     * 员工银行卡所属支行信息
     */
    private String bankBranch;

    /**
     * 员工银行卡预留手机号码
     */
    private String bankReservePhone;


    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证正面照片
     */
    private String idCardFrontUrl;

    /**
     * 身份证背面照片
     */
    private String idCardBackendUrl;


    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 学历
     */
    private String education;

    /**
     * 籍贯
     */
    private String hometown;

    /**
     * 民族
     */
    private String nationality;


    /**
     * 员工当前等级，整型
     */
    private Integer level;
    /**
     * 积分
     */
    private Integer points;

    /**
     * 当前安全上工总时长
     */
    private BigDecimal totalWorkTime;

    /**
     * 当月累计工时
     */
    private BigDecimal currentMonthTime;

    /**
     * 剩余没计算的工作时长
     */
    private BigDecimal leftWorkTime;

    private String province;

    private String city;



}
