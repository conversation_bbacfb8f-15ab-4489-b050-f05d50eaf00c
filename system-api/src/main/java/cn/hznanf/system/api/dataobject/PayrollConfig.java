package cn.hznanf.system.api.dataobject;


import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 薪资档案
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("payroll_config")
@ApiModel(value = "PayrollConfig对象", description = "薪资档案")
public class PayrollConfig extends BaseDO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("状态")
    @TableField("status")
    private String status;

    @ApiModelProperty("基本工资")
    @TableField("base_salary")
    private BigDecimal baseSalary;

    @ApiModelProperty("社保基数")
    @TableField("social_security_base")
    private BigDecimal socialSecurityBase;

    @ApiModelProperty("公积金基数")
    @TableField("provident_fund_base")
    private BigDecimal providentFundBase;

    @ApiModelProperty("参保地")
    @TableField("social_base")
    private String socialBase;

    @ApiModelProperty("子女教育")
    @TableField("children_edu")
    private BigDecimal childrenEdu;

    @ApiModelProperty("继续教育")
    @TableField("continuous_learning")
    private BigDecimal continuousLearning;

    @ApiModelProperty("住房贷款")
    @TableField("housing_loan")
    private BigDecimal housingLoan;

    @ApiModelProperty("住房租金")
    @TableField("renting")
    private BigDecimal renting;

    @ApiModelProperty("	赡养老人")
    @TableField("care_for_the_elderly")
    private BigDecimal careForTheElderly;

    @ApiModelProperty("婴幼儿照护")
    @TableField("baby_care")
    private BigDecimal babyCare;

    @ApiModelProperty("工资工时")
    @TableField("standard_hours")
    private BigDecimal standardHours;

    @ApiModelProperty("员工id")
    @TableField("employee_id")
    private Long employeeId;

    @ApiModelProperty("是否根据基本工资来算工资，true是，false-否")
    @TableField("cal_by_base")
    private Boolean calByBase;
}

