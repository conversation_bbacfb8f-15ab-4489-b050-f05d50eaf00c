package cn.hznanf.system.api.dataobject;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据表
 *
 * <AUTHOR>
 */
@TableName("system_address")
@Data
@EqualsAndHashCode(callSuper = true)
public class AddressDO extends BaseDO {


    private String province;

    private String city;

    private String street;

    private String region;

    private String code;

}
