import static java.lang.System.out;

import cn.hznanf.common.pojo.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

// 演示例子，执行 main 方法控制台输入模块表名回车自动生成对应项目目录中
public class CodeGenerator {

    /**
     * 当前要生成的模块
     */
    private static final ModuleEnum TO_GENERATE_MODULE = ModuleEnum.OTHER;
    /**
     * 当前要生成的表
     */
    private static final String[] TO_GENERATE_TABLE_NAME = {"employee_contract"};

    public static void main(String[] args) {

        FastAutoGenerator.create("**************************************************************************************************************************************************************************************", "root", "Test123654=").globalConfig(builder -> builder.outputDir(System.getProperty("user.dir") + TO_GENERATE_MODULE.getModuleService() + "/src/main/java") //输出到哪个目录
                        .author("generator").enableSwagger().dateType(DateType.TIME_PACK)).packageConfig(builder -> builder.parent(TO_GENERATE_MODULE.modulePackage) // 父包名
                        .entity("dataobject").service("service").serviceImpl("service.impl").mapper("mapper").xml("mapper.xml").controller("controller")).templateConfig(builder -> builder.entity("/templates/entity.java").service("/templates/service.java").serviceImpl("/templates/serviceImpl.java").mapper("/templates/mapper.java").xml("/templates/mapper.xml").controller("/templates/controller.java")).injectionConfig(builder -> builder.beforeOutputFile((tableInfo, objectMap) -> {
                            out.println("tableInfo: " + tableInfo.getEntityName() + " objectMap: " + objectMap.size());
                        }).customFile(new CustomFile.Builder().fileName("DTO.java").filePath(System.getProperty("user.dir") + TO_GENERATE_MODULE.getModuleService() + "/src/main/java").enableFileOverride().templatePath("templates/dto.java.ftl").packageName(TO_GENERATE_MODULE.modulePackage + "/dto").build()).customFile(new CustomFile.Builder().fileName("VO.java").filePath(System.getProperty("user.dir") + TO_GENERATE_MODULE.getModuleService() + "/src/main/java").enableFileOverride().templatePath("templates/vo.java.ftl").packageName(TO_GENERATE_MODULE.modulePackage + "/vo").build())
                        .customFile(new CustomFile.Builder().fileName("Query.java")
                                .filePath(System.getProperty("user.dir") + TO_GENERATE_MODULE.getModuleService() + "/src/main/java").enableFileOverride()
                                .templatePath("templates/paramQuery.java.ftl")
                                .packageName(TO_GENERATE_MODULE.modulePackage + "/dto/query").build())
                        .customFile(new CustomFile.Builder().fileName("Convert.java")
                                .filePath(System.getProperty("user.dir") + TO_GENERATE_MODULE.getModuleService() + "/src/main/java").enableFileOverride()
                                .templatePath("templates/convert.java.ftl")
                                .packageName(TO_GENERATE_MODULE.modulePackage + "/convert").build())

                ).strategyConfig(builder ->
                        // 策略配置
                        builder.enableCapitalMode()  // 开启大写命名
                                .addInclude(TO_GENERATE_TABLE_NAME)   // 需要加载的表
                ).strategyConfig(builder -> builder.entityBuilder().enableFileOverride()  // 覆盖已生成文件
                        .enableChainModel().enableLombok().enableTableFieldAnnotation().superClass(BaseDO.class).addIgnoreColumns(Arrays.asList("create_time", "update_time", "creator", "updater", "deleted")).idType(IdType.AUTO)   // 主键的ID类型
                ).strategyConfig(builder -> builder.controllerBuilder().enableFileOverride()  //  覆盖已生成文件	默认值:false
                        .enableHyphenStyle().enableRestStyle())
                .strategyConfig(builder -> builder.serviceBuilder().formatServiceFileName("%sService")
                        .formatServiceImplFileName("%sServiceImpl"))
                .templateEngine(new FreemarkerTemplateEngine()).execute();

    }


    @AllArgsConstructor
    enum ModuleEnum {

        /**
         * module
         */

        OTHER("/admin", "cn.hznanf.oa.api", "/oa-api"),
        ;

        @Getter
        private String modulePath;
        @Getter
        private String modulePackage;
        @Getter
        private String moduleService;

    }


}