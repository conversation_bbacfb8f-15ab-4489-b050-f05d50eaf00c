//package cn.hznanf.web.cron;
//
//import cn.hznanf.oa.api.service.PayrollService;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//
///**
// * description 工资计算
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2023/6/28 21:58:53
// */
//@Component
//@Slf4j
//public class PayrollScheduledTask {
//
//    @Resource
//    private PayrollService payrollService;
//
//
//    /**
//     * 每月的1日的凌晨2点调整任务
//     */
////    @Scheduled(cron = "0 1 * * * ?")
////    private void create() {
////        //查找打过卡的员工
////        LocalDate now = LocalDate.now();
////        payrollService.calByTask(now);
////    }
//
//
//    @Scheduled(cron = "0 0 1 * * ?")
//    private void create() {
//        log.info("工资计算定时任务开始执行");
//        payrollService.dealUnconfirmed();
//        log.info("工资计算定时任务执行结束");
//    }
////
////    @PostConstruct
////    private void fixPayRollData() {
////        payrollService.fixPayRollData();
////    }
//
//
//}
