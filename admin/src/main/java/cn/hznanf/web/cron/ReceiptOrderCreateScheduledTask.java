//package cn.hznanf.web.cron;
//
//import cn.hznanf.system.api.service.project.ReceiptOrderService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//
///**
// * description 每天扫描，根据生产合同创建收款单
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2023/6/28 21:58:53
// */
//@Component
//@Slf4j
//public class ReceiptOrderCreateScheduledTask {
//
//    @Resource
//    private ReceiptOrderService receiptOrderService;
//
//    /**
//     * 每两分钟去扫描收款单
//     */
//    @Scheduled(cron = "0 */2 * * * ?")
//    private void create() {
//        receiptOrderService.autoCreate();
//    }
//
//}
