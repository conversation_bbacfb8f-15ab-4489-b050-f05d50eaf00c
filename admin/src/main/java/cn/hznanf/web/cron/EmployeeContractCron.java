//package cn.hznanf.web.cron;
//
//
//import cn.hznanf.oa.api.service.EmployeeContractService;
//import javax.annotation.Resource;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//@Component
//public class EmployeeContractCron {
//
//  @Resource
//  private EmployeeContractService employeeContractService;
//
//  /**
//   * 每天凌晨一点判断合同的到期时间，然后更新是否到期已经剩余多少天数到期
//   */
//  @Scheduled(cron = "0 0/5 * * * *")
//  private void updateContractStatus() {
//    employeeContractService.updateContractStatus();
//  }
//
//
//
//}
