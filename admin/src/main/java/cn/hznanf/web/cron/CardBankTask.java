//package cn.hznanf.web.cron;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hznanf.system.api.dataobject.EmployeeDetails;
//import cn.hznanf.system.api.service.user.EmployeeDetailsService;
//import com.alibaba.fastjson.JSONObject;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.Resource;
//import org.springframework.core.io.ResourceLoader;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
//import javax.annotation.PostConstruct;
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Component
//public class CardBankTask {
//
//    @Autowired
//    private EmployeeDetailsService employeeDetailsService;
//    @Autowired
//    private RestTemplate restTemplate;
//    @Autowired
//    private ResourceLoader resourceLoader;
//
//    private Map<String, String> bankMap = new HashMap<>();
//
//    public Map<String, String> readConfig() throws IOException {
//        Resource resource = resourceLoader.getResource("classpath:bank.json");
//        ObjectMapper objectMapper = new ObjectMapper();
//        return objectMapper.readValue(resource.getInputStream(), Map.class);
//    }
//
//    @Scheduled(fixedRate = 300000)
//    private void findBankName() {
//        if (bankMap.isEmpty()) {
//            try {
//                bankMap = readConfig();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//
//        List<EmployeeDetails> employeeDetails = employeeDetailsService.selectEmptyBranch();
//        if (CollectionUtil.isEmpty(employeeDetails)) {
//            return;
//        }
//        String url = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo={cardNo}&cardBinCheck=true";
//        for (EmployeeDetails employeeDetail : employeeDetails) {
//            Map<String, String> map = new HashMap<>();
//            map.put("cardNo", employeeDetail.getBankCard());
//            String jsonStr = restTemplate.getForObject(url, String.class, map);
//            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
//            if (jsonObject.getBoolean("validated")) {
//                String bank = jsonObject.getString("bank");
//                String bankName = bankMap.get(bank);
//                employeeDetail.setBankBranch(bankName);
//            }
//        }
//        employeeDetailsService.updateBatchById(employeeDetails);
//
//    }
//
//}
