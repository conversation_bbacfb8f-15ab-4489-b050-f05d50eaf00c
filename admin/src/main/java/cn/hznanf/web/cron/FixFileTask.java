package cn.hznanf.web.cron;

import cn.hznanf.oa.api.dataobject.EmployeeContract;
import cn.hznanf.oa.api.service.EmployeeContractService;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CopyObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 修复pdf文件格式问题
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/8/10
 */
@Component
@Slf4j
public class FixFileTask {

  @Resource
  private EmployeeContractService employeeContractService;

  @Resource
  private OSS ossClient;

  @Value("${oss.bucketName}")
  private String bucketName;

  // 阿里云OSS域名前缀
  private static final String OSS_DOMAIN = "https://hznfsb.oss-cn-hangzhou.aliyuncs.com/";

  // 匹配没有文件后缀的URL的正则表达式
  private static final Pattern NO_EXTENSION_PATTERN = Pattern.compile(".*[^./]$");

  /**
   * 修复员工合同附件URL缺少文件后缀的问题
   * 每天凌晨2点执行一次
   */
  @PostConstruct
  public void fixContractAttachmentUrls() {
    log.info("开始执行文件修复任务：修复员工合同附件URL缺少文件后缀的问题");

    try {
      // 查询所有有附件的员工合同
      LambdaQueryWrapper<EmployeeContract> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.isNotNull(EmployeeContract::getAttachments);
      List<EmployeeContract> contracts = employeeContractService.list(queryWrapper);

      if (CollectionUtils.isEmpty(contracts)) {
        log.info("没有找到需要修复的员工合同记录");
        return;
      }

      int fixedCount = 0;
      int totalProcessed = 0;

      for (EmployeeContract contract : contracts) {
        totalProcessed++;
        List<String> attachments = contract.getAttachments();

        if (CollectionUtils.isEmpty(attachments)) {
          continue;
        }

        List<String> fixedAttachments = new ArrayList<>();
        boolean needUpdate = false;

        for (String attachment : attachments) {
          String fixedUrl = fixAttachmentUrl(attachment);
          fixedAttachments.add(fixedUrl);

          if (!attachment.equals(fixedUrl)) {
            needUpdate = true;
            log.info("修复URL: {} -> {}", attachment, fixedUrl);
          }
        }

        // 如果有URL被修复，则更新数据库
        if (needUpdate) {
          contract.setAttachments(fixedAttachments);
          employeeContractService.updateById(contract);
          fixedCount++;
          log.info("已修复员工合同ID: {}, 员工ID: {}", contract.getId(), contract.getEmployeeId());
        }
      }

      log.info("文件修复任务完成，共处理 {} 条记录，修复 {} 条记录", totalProcessed, fixedCount);

    } catch (Exception e) {
      log.error("执行文件修复任务时发生异常", e);
    }
  }

  /**
   * 修复单个附件URL
   *
   * @param originalUrl 原始URL
   * @return 修复后的URL
   */
  private String fixAttachmentUrl(String originalUrl) {
    if (!StringUtils.hasText(originalUrl)) {
      return originalUrl;
    }

    // 如果URL不是以OSS域名开头，直接返回
    if (!originalUrl.startsWith(OSS_DOMAIN)) {
      return originalUrl;
    }

    // 如果URL已经有文件后缀，直接返回
    if (!NO_EXTENSION_PATTERN.matcher(originalUrl).matches()) {
      return originalUrl;
    }

    try {
      // 提取OSS对象键（去掉域名前缀）
      String originalObjectKey = originalUrl.substring(OSS_DOMAIN.length());

      // 检查OSS中的文件类型
      String detectedExtension = detectFileExtension(originalObjectKey);

      if (StringUtils.hasText(detectedExtension)) {
        // 生成新的对象键（带扩展名）
        String newObjectKey = originalObjectKey + detectedExtension;

        // 在OSS中复制文件到新的键名
        boolean copySuccess = copyOssObject(originalObjectKey, newObjectKey);

        if (copySuccess) {
          // 删除原始文件
          deleteOssObject(originalObjectKey);

          // 返回新的URL
          return OSS_DOMAIN + newObjectKey;
        } else {
          log.warn("OSS文件复制失败，保持原URL不变: {}", originalUrl);
          return originalUrl;
        }
      }

    } catch (Exception e) {
      log.warn("修复文件失败，URL: {}, 错误: {}", originalUrl, e.getMessage());
    }

    return originalUrl;
  }

  /**
   * 检测OSS文件的扩展名
   *
   * @param objectKey OSS对象键
   * @return 文件扩展名（包含点号），如果检测失败返回null
   */
  private String detectFileExtension(String objectKey) {
    try {
      // 获取文件的元数据
      ObjectMetadata metadata = ossClient.getObjectMetadata(bucketName, objectKey);
      String contentType = metadata.getContentType();

      if (StringUtils.hasText(contentType)) {
        // 根据Content-Type推断文件扩展名
        return getExtensionFromContentType(contentType);
      }

    } catch (Exception e) {
      log.debug("获取文件元数据失败，objectKey: {}, 错误: {}", objectKey, e.getMessage());
    }

    // 如果无法从元数据获取，默认返回.pdf（根据业务需求，大部分合同文件都是PDF）
    return ".pdf";
  }

  /**
   * 根据Content-Type获取文件扩展名
   *
   * @param contentType MIME类型
   * @return 文件扩展名
   */
  private String getExtensionFromContentType(String contentType) {
    if (contentType == null) {
      return ".pdf";
    }

    switch (contentType.toLowerCase()) {
      case "application/pdf":
        return ".pdf";
      case "image/jpeg":
      case "image/jpg":
        return ".jpg";
      case "image/png":
        return ".png";
      case "image/gif":
        return ".gif";
      case "application/msword":
        return ".doc";
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return ".docx";
      case "application/vnd.ms-excel":
        return ".xls";
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return ".xlsx";
      case "text/plain":
        return ".txt";
      default:
        // 默认返回.pdf，因为合同文件通常是PDF格式
        return ".pdf";
    }
  }

  /**
   * 在OSS中复制对象到新的键名
   *
   * @param sourceKey 源对象键
   * @param targetKey 目标对象键
   * @return 是否复制成功
   */
  private boolean copyOssObject(String sourceKey, String targetKey) {
    try {
      // 检查源文件是否存在
      if (!ossClient.doesObjectExist(bucketName, sourceKey)) {
        log.warn("源文件不存在: {}", sourceKey);
        return false;
      }

      // 检查目标文件是否已存在
      if (ossClient.doesObjectExist(bucketName, targetKey)) {
        log.info("目标文件已存在，跳过复制: {}", targetKey);
        return true;
      }

      // 创建复制请求
      CopyObjectRequest copyObjectRequest = new CopyObjectRequest(bucketName, sourceKey, bucketName, targetKey);

      // 执行复制
      ossClient.copyObject(copyObjectRequest);

      log.info("OSS文件复制成功: {} -> {}", sourceKey, targetKey);
      return true;

    } catch (Exception e) {
      log.error("OSS文件复制失败: {} -> {}, 错误: {}", sourceKey, targetKey, e.getMessage());
      return false;
    }
  }

  /**
   * 删除OSS对象
   *
   * @param objectKey 对象键
   * @return 是否删除成功
   */
  private boolean deleteOssObject(String objectKey) {
    try {
      // 检查文件是否存在
      if (!ossClient.doesObjectExist(bucketName, objectKey)) {
        log.info("文件不存在，无需删除: {}", objectKey);
        return true;
      }

      // 删除文件
      ossClient.deleteObject(bucketName, objectKey);

      log.info("OSS文件删除成功: {}", objectKey);
      return true;

    } catch (Exception e) {
      log.error("OSS文件删除失败: {}, 错误: {}", objectKey, e.getMessage());
      return false;
    }
  }

  /**
   * 手动执行修复任务（用于测试或紧急修复）
   */
  public void manualFixContractAttachmentUrls() {
    log.info("手动执行文件修复任务");
    fixContractAttachmentUrls();
  }

  /**
   * 测试单个URL修复（用于调试）
   *
   * @param testUrl 测试URL
   * @return 修复后的URL
   */
  public String testFixSingleUrl(String testUrl) {
    log.info("测试修复单个URL: {}", testUrl);
    String fixedUrl = fixAttachmentUrl(testUrl);
    log.info("修复结果: {} -> {}", testUrl, fixedUrl);
    return fixedUrl;
  }
}
