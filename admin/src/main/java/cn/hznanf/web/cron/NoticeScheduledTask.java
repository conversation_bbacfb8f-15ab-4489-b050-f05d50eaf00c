//package cn.hznanf.web.cron;
//
//import cn.hznanf.oa.api.service.ApprovalInfoService;
//import cn.hznanf.oa.api.service.EmployeeAttendanceService;
//import cn.hznanf.system.api.service.ITodoService;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//public class NoticeScheduledTask {
//
//    @Resource
//    private ApprovalInfoService approvalInfoService;
//    @Resource
//    private EmployeeAttendanceService attendanceService;
//
//    @Resource
//    private ITodoService todoService;
//
//
//    /**
//     * 每天晚上20.15分提醒项目经理当日的工时审核提醒
//     */
//    @Scheduled(cron = "0 15 20 * * *")
//    private void nightNotice() {
//        todoService.notice();
////        approvalInfoService.noticePunch();
//    }
//
//
//    /**
//     * 每天早上8点提醒项目经理过往遗忘的工时审核
//     */
//    @Scheduled(cron = "0 0 8 * * *")
//    private void morningNotice() {
//        todoService.notice();
////        approvalInfoService.noticePunch();
//    }
//
////    @Scheduled(cron = "0 30 17 * * *")
////    private void noticeEmployeePunch() {
////        attendanceService.noticeEmployeePunch();
////    }
//
//}
