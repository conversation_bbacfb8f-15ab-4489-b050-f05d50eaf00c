//package cn.hznanf.web.cron;
//
//import cn.hznanf.system.api.service.permission.MenuService;
//import cn.hznanf.system.api.service.permission.PermissionService;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//public class PermissionCache {
//
//    @Resource
//    private MenuService menuService;
//
//    @Resource
//    private PermissionService permissionService;
//
//    /**
//     * 每分钟刷新一次缓存
//     */
//    @Scheduled(cron = "0 0/1 * * * *")
//    private void cacheMenu() {
//
//
//        menuService.initLocalCache();
//
//        permissionService.initLocalCache();
//
//    }
//
//
//
//}
