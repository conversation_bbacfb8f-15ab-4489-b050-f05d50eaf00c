//package cn.hznanf.web.cron;
//
//import cn.hznanf.system.api.dataobject.Employee;
//import cn.hznanf.system.api.service.user.EmployeeService;
//import java.util.List;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//
///**
// * description 每天扫描，根据生产合同创建收款单
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2023/6/28 21:58:53
// */
//@Component
//@Slf4j
//public class CalOaOpenIdTask {
//
//    @Value("${cron.switch.getopenid:false}")
//    private Boolean openIdSwitch;
//
//    @Resource
//    private EmployeeService employeeService;
//
//    /**
//     * 填充openId
//     */
//    @Scheduled(cron = "0 */10 * * * ?")
//    private void create() {
//        if (!openIdSwitch) {
//            return;
//        }
//        log.info("get employee oa openId");
//        List<Employee> employees = employeeService.selectAllEmptyUnionId();
//        employeeService.calOaOpenId(employees);
//    }
//
//}
