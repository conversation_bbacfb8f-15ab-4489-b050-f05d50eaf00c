//package cn.hznanf.web.cron;
//
//import cn.hznanf.system.api.service.purchase.InOutBoundOrderService;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//
///**
// * description 每五分钟扫描一次
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2023/6/28 21:58:53
// */
//@Component
//@Slf4j
//public class InBoundOrderScheduledTask {
//
//  @Resource
//  private InOutBoundOrderService inOutBoundOrderService;
//
//  /**
//   * 修改入库单状态
//   */
//  @Scheduled(cron = "0 */5 * * * ?")
//  private void create() {
//
//    inOutBoundOrderService.dealUnSettled();
//
//  }
//
//}
