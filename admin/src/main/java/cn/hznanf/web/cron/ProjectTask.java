//package cn.hznanf.web.cron;
//
//
//import cn.hznanf.system.api.service.project.ProjectService;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//public class ProjectTask {
//
//    @Resource
//    private ProjectService projectService;
//
//
//    //每天凌晨记录下我这个项目今天需要打卡的员工列表
//    @Scheduled(cron = "0 0 1 * * *")
//    private void insertProjectLog() {
//
//        projectService.insertRelationLog();
//
//    }
//}
