package cn.hznanf.web.controller.project;

import static cn.hznanf.common.pojo.CommonResult.success;

import cn.hznanf.common.pojo.CommonResult;
import cn.hznanf.common.pojo.PageResult;
import cn.hznanf.oa.api.service.ClaimForRemService;
import cn.hznanf.oa.api.vo.ClaimForRemVO;
import cn.hznanf.system.api.convert.ProjectConvert;
import cn.hznanf.system.api.vo.finance.PaymentExportVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeeCostExportVO;
import cn.hznanf.system.api.RequestTimeHolder;
import cn.hznanf.system.api.WebFrameworkUtils;
import cn.hznanf.system.api.convert.AllocateOrderConvert;
import cn.hznanf.system.api.convert.PaymentConvert;
import cn.hznanf.system.api.dataobject.project.WorkShift;
import cn.hznanf.system.api.dto.LoginUser;
import cn.hznanf.system.api.service.finance.PaymentService;
import cn.hznanf.system.api.service.project.ProjectService;
import cn.hznanf.system.api.service.project.WorkShiftService;
import cn.hznanf.system.api.service.purchase.AllocateOrderService;
import cn.hznanf.system.api.vo.finance.PaymentVO;
import cn.hznanf.system.api.vo.project.ProjectAttachmentSaveReqVO;
import cn.hznanf.system.api.vo.project.ProjectAttachmentsPageReqVO;
import cn.hznanf.system.api.vo.project.ProjectAttachmentsPageRespVO;
import cn.hznanf.system.api.vo.project.ProjectBaseRespVO;
import cn.hznanf.system.api.vo.project.ProjectCostExportVO;
import cn.hznanf.system.api.vo.project.ProjectCostPageReqVO;
import cn.hznanf.system.api.vo.project.ProjectCreateReqVO;
import cn.hznanf.system.api.vo.project.ProjectDashboardRespVO;
import cn.hznanf.system.api.vo.project.ProjectDetailRespVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeeCostVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeePageReqVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeePageRespVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeeRelationReqVO;
import cn.hznanf.system.api.vo.project.ProjectEmployeeRespVO;
import cn.hznanf.system.api.vo.project.ProjectPageReqVO;
import cn.hznanf.system.api.vo.project.ProjectPageRespVO;
import cn.hznanf.system.api.vo.project.ProjectPayLogExport;
import cn.hznanf.system.api.vo.project.ProjectUpdateReqVO;
import cn.hznanf.system.api.vo.project.WorkShiftSaveVO;
import cn.hznanf.system.api.vo.purchase.AllocateOrderLineExtExportVO;
import cn.hznanf.system.api.vo.purchase.AllocateOrderLineExtVO;
import cn.hznanf.web.ProjectAdminService;
import cn.hznanf.web.util.ExcelUtils;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "管理后台-项目管理")
@RestController
@RequestMapping("/project/manager")
@Validated
@Slf4j
public class ProjectController {

  @Resource
  private ProjectService projectService;
  @Resource
  private WorkShiftService workShiftService;
  @Resource
  private ProjectAdminService projectAdminService;
  @Resource
  private AllocateOrderService allocateOrderService;

  @Resource
  private  PaymentService paymentService;
  @Resource
  private ClaimForRemService claimForRemService;



  @PostMapping("/create")
  @Operation(summary = "创建新项目")
  @PreAuthorize("@ss.hasPermission('project:manager:create')")
  public CommonResult<Long> createProject(@Valid @RequestBody ProjectCreateReqVO req) {
    Long id = projectService.createProject(req);
    return success(id);
  }

  @PutMapping("/update")
  @Operation(summary = "修改项目数据")
  @PreAuthorize("@ss.hasPermission('project:manager:update')")
  public CommonResult<Boolean> update(@Valid @RequestBody ProjectUpdateReqVO reqVO) {
    projectService.updateById(reqVO);
    return success(true);
  }

  @DeleteMapping("/delete")
  @Operation(summary = "删除项目数据")
  @Parameter(name = "id", description = "编号", required = true, example = "1024")
  @PreAuthorize("@ss.hasPermission('project:manager:delete')")
  public CommonResult<Boolean> deleteById(@RequestParam("id") Long id) {
    projectService.deleteById(id);
    return success(true);
  }

  @GetMapping("/page")
  @Operation(summary = "/获得项目数据分页列表")
//    @PreAuthorize("@ss.hasPermission('project:manager:query')")
  public CommonResult<PageResult<ProjectPageRespVO>> getPage(@Valid ProjectPageReqVO reqVO) {
    final PageResult<ProjectPageRespVO> projectDataPage = projectService.getProjectDataPage(reqVO);
    return success(projectDataPage);
  }

  @GetMapping("/list-all-simple")
  @Operation(summary = "获取项目精简信息列表")
  public CommonResult<List<ProjectBaseRespVO>> getSimpleList() {
    List<ProjectBaseRespVO> list = projectService.selectList();
    return success(list);
  }

  @GetMapping(value = "/get")
  @Operation(summary = "/查询项目数据详细")
  @Parameter(name = "id", description = "编号", required = true, example = "1024")
  @PreAuthorize("@ss.hasPermission('project:manager:query')")
  public CommonResult<ProjectDetailRespVO> getDetail(@RequestParam("id") Long id) {
    return success(projectService.getProjectDetail(id));
  }

  @GetMapping(value = "/commit")
  @Operation(summary = "/提交")
  @Parameter(name = "id", description = "编号", required = true, example = "1024")
  @PreAuthorize("@ss.hasPermission('project:manager:commit')")
  public CommonResult<Long> commit(@RequestParam("id") Long id) {
    projectService.commit(id);
    return success(id);
  }

  @GetMapping(value = "/terminate")
  @Operation(summary = "/项目终止")
  @Parameter(name = "id", description = "编号", required = true)
  @PreAuthorize("@ss.hasPermission('project:manager:terminate')")
  public CommonResult<Long> terminate(@RequestParam("id") Long id) {
    projectService.terminate(id);
    return success(id);
  }

  @GetMapping(value = "/paused")
  @Operation(summary = "/项目暂停")
  @Parameter(name = "id", description = "编号", required = true)
  @PreAuthorize("@ss.hasPermission('project:manager:paused')")
  public CommonResult<Long> paused(@RequestParam("id") Long id) {
    projectService.paused(id);
    return success(id);
  }

  @GetMapping(value = "/finish")
  @Operation(summary = "/项目结束")
  @Parameter(name = "id", description = "编号", required = true)
  @PreAuthorize("@ss.hasPermission('project:manager:finish')")
  public CommonResult<Long> finish(@RequestParam("id") Long id) {
    projectService.finish(id);
    return success(id);
  }

  @PostMapping(value = "/addEmployee")
  @Operation(summary = "/新增项目组成员")
//    @PreAuthorize("@ss.hasPermission('project:manager:addEmployee')")
  public CommonResult<Boolean> addEmployee(@Valid @RequestBody ProjectEmployeeRelationReqVO req) {
    log.info("用户[{}],正在为项目id为[{}],添加员工[{}]", WebFrameworkUtils.getLoginUserId(),
        req.getId(), JSONObject.toJSONString(req.getEmployeeIds()));
    projectService.addEmployee(req);
    return success(true);
  }


  @DeleteMapping(value = "/removeEmployee")
  @Operation(summary = "/移除项目组成员")
//    @PreAuthorize("@ss.hasPermission('project:manager:removeEmployee')")
  public CommonResult<Boolean> removeEmployee(
      @Valid @RequestBody ProjectEmployeeRelationReqVO req) {
    log.info("用户[{}],正在为项目id为[{}],移除员工[{}]", WebFrameworkUtils.getLoginUserId(),
        req.getId(), JSONObject.toJSONString(req.getEmployeeIds()));
    projectService.removeEmployee(req);
    return success(true);
  }

  @GetMapping(value = "/employeePage")
  @Operation(summary = "/查看项目组成员")
//    @PreAuthorize("@ss.hasPermission('project:manager:user')")
  public CommonResult<PageResult<ProjectEmployeePageRespVO>> employeePage(
      @Valid ProjectEmployeePageReqVO reqVO) {
    log.info("用户[{}],正在查看项目id为[{}]的员工", WebFrameworkUtils.getLoginUserId(),
        reqVO.getId());
    reqVO.setPageSize(1000);
    PageResult<ProjectEmployeePageRespVO> page = projectService.employeePage(reqVO);
    log.info("用户[{}],正在查看项目id为[{}]的员工，返回的结果为[{}]",
        WebFrameworkUtils.getLoginUserId(), reqVO.getId(), JSONObject.toJSONString(page.getList()));
    return success(page);
  }


  @GetMapping(value = "/projectAllEmployee")
  @Operation(summary = "/查看项目组成员")
  public CommonResult<PageResult<ProjectEmployeeRespVO>> projectAllEmployee(
      @Valid ProjectEmployeePageReqVO reqVO) {
    reqVO.setPageSize(500);
    PageResult<ProjectEmployeeRespVO> page = projectAdminService.projectAllEmployee(reqVO);
    return success(page);
  }

  @PostMapping(value = "/updateWorkShift")
  @Operation(summary = "/修改打卡时间设置")
  @PreAuthorize("@ss.hasPermission('project:manager:updateWorkShift')")
  public CommonResult<Boolean> updateWorkShift(@Valid @RequestBody List<WorkShiftSaveVO> req) {
    workShiftService.updateWorkShift(req);
    return success(true);
  }

  @GetMapping(value = "/getWorkShift")
  @Operation(summary = "/查询项目当前打卡时间设置")
  @PreAuthorize("@ss.hasPermission('project:manager:getWorkShift')")
  public CommonResult<List<WorkShift>> getWorkShift(@RequestParam("id") Long id) {
    List<WorkShift> workShiftByProjectId = workShiftService.getWorkShiftByProjectId(id);
    return success(workShiftByProjectId);
  }

  @PostMapping("/uploadFileOfProject")
  @Operation(summary = "/上传项目相关的材料")
//    @PreAuthorize("@ss.hasPermission('project:manager:uploadFileOfProject')")
  public CommonResult<Boolean> uploadFileOfProject(@RequestBody ProjectAttachmentSaveReqVO reqVO) {
    projectService.uploadFileOfProject(reqVO);
    return success(true);
  }


  @GetMapping("/attachmentsPage")
  @Operation(summary = "/项目附件材料分页")
//    @PreAuthorize("@ss.hasPermission('project:manager:attachmentsPage')")
  public CommonResult<PageResult<ProjectAttachmentsPageRespVO>> attachmentsPage(
      @Valid ProjectAttachmentsPageReqVO reqVO) {
    final PageResult<ProjectAttachmentsPageRespVO> projectDataPage = projectService.attachmentsPage(
        reqVO);
    return success(projectDataPage);
  }


  @GetMapping(value = "/dashboard")
  @Operation(summary = "/项目大盘数据")
  @Parameter(name = "id", description = "编号", required = true, example = "1024")
  @PreAuthorize("@ss.hasPermission('project:manager:query')")
  public CommonResult<ProjectDashboardRespVO> dashboard(@RequestParam("id") Long id) {
    return success(projectService.dashboard(id));
  }

  @GetMapping(value = "/employeeCost")
  @Operation(summary = "/查看项目人天工时消耗")
  public CommonResult<List<ProjectEmployeeCostVO>> employeeCost(@RequestParam("id") Long id) {
    List<ProjectEmployeeCostVO> res = projectService.employeeCost(id);
    return success(res);
  }

  @GetMapping("/cost/page")
  @Operation(summary = "工时列表")
  public CommonResult<PageResult<ProjectEmployeeCostVO>> costPage(
      @Valid ProjectCostPageReqVO reqVO) {
    final PageResult<ProjectEmployeeCostVO> projectDataPage = projectService.costPage(reqVO);
    return success(projectDataPage);
  }


  @PostMapping("/exportData")
  public void exportData(@RequestBody ProjectPayLogExport reqVO, HttpServletResponse response) {
    String fileName;
    if (reqVO.getType() == 1) {
      fileName = "采购导出" + System.currentTimeMillis() + ".xls";
      List<ProjectCostExportVO> exportPayLog = projectService.getExportPayLog(reqVO);
      try {
        ExcelUtils.write(response, fileName, "采购导出", ProjectCostExportVO.class, exportPayLog);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    } else if (reqVO.getType() == 0) {
      fileName = "调拨导出" + System.currentTimeMillis() + ".xls";
      List<AllocateOrderLineExtVO> lineList = allocateOrderService.getLineList(reqVO);
      List<AllocateOrderLineExtExportVO> exportVOList = AllocateOrderConvert.INSTANCE.ext2Export(lineList);
      try {
        ExcelUtils.write(response, fileName, "调拨导出", AllocateOrderLineExtExportVO.class, exportVOList);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    } else if (reqVO.getType() == 2) {
      List<ProjectEmployeeCostVO> projectEmployeeCostVOS = projectService.costExport(reqVO);
      List<ProjectEmployeeCostExportVO> projectEmployeeCostExportVOS = ProjectConvert.INSTANCE.projectEmployeeCost2Export(
          projectEmployeeCostVOS);
      fileName = "人力导出" + System.currentTimeMillis() + ".xls";
      try {
        ExcelUtils.write(response, fileName, "采购导出", ProjectEmployeeCostExportVO.class, projectEmployeeCostExportVOS);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    } else if (reqVO.getType() == 3) {
      fileName = "付款单导出" + System.currentTimeMillis() + ".xls";
      List<PaymentVO> paymentVOS = paymentService.exportPayment(reqVO);
      List<PaymentExportVO> exportPayLog = PaymentConvert.INSTANCE.payment2Export(paymentVOS);
      try {
        ExcelUtils.write(response, fileName, "付款单导出", PaymentExportVO.class, exportPayLog);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    } else {
      fileName = "报销导出" + System.currentTimeMillis() + ".xls";
      List<ClaimForRemVO> claimForRemVOS = claimForRemService.exportClaim(reqVO);
      try {
        ExcelUtils.write(response, fileName, "报销导出", ClaimForRemVO.class, claimForRemVOS);
      } catch (IOException e) {
        throw new RuntimeException(e);
      }

    }

  }

  /**
   * 我参加过的项目
   *
   * @return
   */
  @GetMapping("/myJoinProject")
  public CommonResult<List<ProjectBaseRespVO>> getMyJoinProject() {
    LoginUser loginUser = RequestTimeHolder.getLoginUser();
    Long id = loginUser.getId();
    List<ProjectBaseRespVO> projectBaseRespVOS = projectService.selectJoinList(id);
    return CommonResult.success(projectBaseRespVOS);
  }
}
